#!/usr/bin/env python3
"""
真实BIOS连接测试脚本
用于测试实际的BIOS TAB检测和菜单遍历
"""

from bios_menu_explorer import BIOSMenuExplorer, KeyCode
from config import SOL_CONFIG
import time
import json


def test_basic_connection():
    """测试基本SOL连接"""
    print("=== 测试基本SOL连接 ===")
    
    explorer = BIOSMenuExplorer(
        host=SOL_CONFIG['host'],
        username=SOL_CONFIG['username'],
        password=SOL_CONFIG['password']
    )
    
    try:
        print(f"连接到主机: {SOL_CONFIG['host']}")
        if not explorer.start_sol_session():
            print("❌ SOL连接失败")
            return False
        
        print("✅ SOL连接成功")
        
        # 等待界面稳定
        print("等待BIOS界面稳定...")
        time.sleep(3)
        
        # 确保在主页面
        explorer.go_to_main_page()
        time.sleep(2)
        
        print("✅ 基本连接测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        return False
    finally:
        explorer.close_sol_session()


def test_tab_detection():
    """测试TAB检测功能"""
    print("\n=== 测试TAB检测功能 ===")
    
    explorer = BIOSMenuExplorer(
        host=SOL_CONFIG['host'],
        username=SOL_CONFIG['username'],
        password=SOL_CONFIG['password']
    )
    
    try:
        if not explorer.start_sol_session():
            print("❌ SOL连接失败")
            return
        
        print("等待BIOS界面稳定...")
        time.sleep(3)
        explorer.go_to_main_page()
        time.sleep(2)
        
        # 读取当前屏幕内容
        print("读取当前屏幕内容...")
        content = explorer.read_screen_content(timeout=3.0)
        
        if not content:
            print("❌ 未读取到屏幕内容")
            return
        
        print(f"✅ 读取到 {len(content)} 字符的内容")
        
        # 调试屏幕内容
        print("\n--- 调试屏幕内容 ---")
        explorer.debug_screen_content(content, save_to_file=True)
        
        # 测试TAB名称提取
        print("\n--- 测试TAB名称提取 ---")
        tab_name = explorer._extract_tab_name(content, 0)
        print(f"识别的TAB名称: '{tab_name}'")
        
        # 验证是否为Main TAB
        if 'main' in tab_name.lower():
            print("✅ 正确识别为Main TAB")
        else:
            print(f"⚠️  识别的TAB名称可能不正确，期望包含'main'，实际为: {tab_name}")
        
        return tab_name
        
    except Exception as e:
        print(f"❌ TAB检测失败: {e}")
        import traceback
        traceback.print_exc()
        return None
    finally:
        explorer.close_sol_session()


def test_tab_switching():
    """测试TAB切换功能"""
    print("\n=== 测试TAB切换功能 ===")
    
    explorer = BIOSMenuExplorer(
        host=SOL_CONFIG['host'],
        username=SOL_CONFIG['username'],
        password=SOL_CONFIG['password']
    )
    
    try:
        if not explorer.start_sol_session():
            print("❌ SOL连接失败")
            return
        
        print("等待BIOS界面稳定...")
        time.sleep(3)
        explorer.go_to_main_page()
        time.sleep(2)
        
        # 测试切换到下一个TAB
        print("测试切换到下一个TAB...")
        
        # 读取第一个TAB
        content1 = explorer.read_screen_content(timeout=2.0)
        tab_name1 = explorer._extract_tab_name(content1, 0)
        print(f"第一个TAB: {tab_name1}")
        
        # 切换到下一个TAB
        print("发送右箭头键切换TAB...")
        explorer.send_key(KeyCode.RIGHT, delay=1.0)
        time.sleep(2)
        
        # 读取第二个TAB
        content2 = explorer.read_screen_content(timeout=2.0)
        tab_name2 = explorer._extract_tab_name(content2, 1)
        print(f"第二个TAB: {tab_name2}")
        
        # 验证TAB是否发生变化
        if tab_name1 != tab_name2:
            print("✅ TAB切换成功，内容发生变化")
        else:
            print("⚠️  TAB名称未变化，可能切换失败或识别有误")
        
        # 保存两个TAB的内容用于分析
        with open("tab1_debug.txt", "w", encoding="utf-8") as f:
            f.write(f"TAB名称: {tab_name1}\n")
            f.write("=" * 50 + "\n")
            f.write(content1)
        
        with open("tab2_debug.txt", "w", encoding="utf-8") as f:
            f.write(f"TAB名称: {tab_name2}\n")
            f.write("=" * 50 + "\n")
            f.write(content2)
        
        print("TAB内容已保存到 tab1_debug.txt 和 tab2_debug.txt")
        
        return [tab_name1, tab_name2]
        
    except Exception as e:
        print(f"❌ TAB切换测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None
    finally:
        explorer.close_sol_session()


def test_single_tab_exploration():
    """测试单个TAB的遍历"""
    print("\n=== 测试单个TAB遍历 ===")
    
    explorer = BIOSMenuExplorer(
        host=SOL_CONFIG['host'],
        username=SOL_CONFIG['username'],
        password=SOL_CONFIG['password']
    )
    
    try:
        if not explorer.start_sol_session():
            print("❌ SOL连接失败")
            return
        
        print("等待BIOS界面稳定...")
        time.sleep(3)
        explorer.go_to_main_page()
        time.sleep(2)
        
        # 获取当前TAB内容
        content = explorer.read_screen_content(timeout=3.0)
        tab_name = explorer._extract_tab_name(content, 0)
        
        print(f"开始遍历TAB: {tab_name}")
        
        # 遍历当前TAB
        tab_structure = explorer.explore_current_tab(tab_name, 0)
        
        print("✅ TAB遍历完成")
        
        # 保存结果
        result_file = f"single_tab_structure_{tab_name.replace(' ', '_')}.json"
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(tab_structure, f, ensure_ascii=False, indent=2)
        
        print(f"TAB结构已保存到: {result_file}")
        
        # 显示结果摘要
        print(f"\nTAB结构摘要:")
        print(f"  TAB名称: {tab_structure['name']}")
        print(f"  路径: {tab_structure['path']}")
        print(f"  子菜单数: {len(tab_structure['children'])}")
        
        for i, child in enumerate(tab_structure['children'][:5]):  # 显示前5个
            print(f"    {i+1}. {child['name']}")
        
        if len(tab_structure['children']) > 5:
            print(f"    ... 还有 {len(tab_structure['children']) - 5} 个子菜单")
        
        return tab_structure
        
    except Exception as e:
        print(f"❌ TAB遍历失败: {e}")
        import traceback
        traceback.print_exc()
        return None
    finally:
        explorer.close_sol_session()


def main():
    """主测试函数"""
    print("BIOS真实连接测试")
    print("=" * 40)
    
    # 运行测试序列
    tests = [
        ("基本连接测试", test_basic_connection),
        ("TAB检测测试", test_tab_detection),
        ("TAB切换测试", test_tab_switching),
        ("单TAB遍历测试", test_single_tab_exploration)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        
        try:
            result = test_func()
            results[test_name] = result
            
            if result is not None:
                print(f"✅ {test_name} 完成")
            else:
                print(f"⚠️  {test_name} 完成但可能有问题")
                
        except Exception as e:
            print(f"❌ {test_name} 失败: {e}")
            results[test_name] = None
        
        # 测试间隔
        time.sleep(1)
    
    # 显示总结
    print(f"\n{'='*20} 测试总结 {'='*20}")
    for test_name, result in results.items():
        status = "✅ 成功" if result is not None else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print("\n测试完成！")
    print("请检查生成的调试文件:")
    print("- debug_raw_content.txt")
    print("- debug_clean_content.txt") 
    print("- tab1_debug.txt")
    print("- tab2_debug.txt")
    print("- single_tab_structure_*.json")


if __name__ == "__main__":
    main()
