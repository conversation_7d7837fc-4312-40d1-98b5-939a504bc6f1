# BIOS菜单结构遍历器

基于SOL (Serial Over LAN) 的BIOS菜单自动遍历和结构获取工具。

## 功能特性

- 🔍 **自动遍历**: 自动遍历所有BIOS TAB页面和多级子菜单
- 🗂️ **结构化输出**: 生成完整的菜单结构JSON文件
- 🎯 **智能导航**: 支持上下左右键导航、回车进入、ESC退出
- 📊 **详细分析**: 提供菜单统计和结构分析
- ⚙️ **可配置**: 支持自定义遍历参数和解析规则

## 文件结构

```
├── bios_menu_explorer.py    # 核心遍历器类
├── example_usage.py         # 使用示例和演示
├── config.py               # 配置文件
├── simple_git_bios_info.py # 原始参考代码
└── README.md               # 说明文档
```

## 快速开始

### 1. 基本使用

```python
from bios_menu_explorer import BIOSMenuExplorer

# 创建遍历器实例
explorer = BIOSMenuExplorer(
    host="************",
    username="Administrator", 
    password="Superuser9!"
)

# 运行完整遍历
menu_structure = explorer.run_full_exploration()
```

### 2. 运行示例

```bash
# 运行完整遍历示例
python example_usage.py

# 选择不同的示例模式
# 1. 完整遍历 (推荐)
# 2. 手动导航演示  
# 3. 分析已保存的结构
# 4. 创建菜单摘要
```

### 3. 配置验证

```bash
# 验证配置参数
python config.py
```

## 核心类和方法

### BIOSMenuExplorer

主要的BIOS菜单遍历器类。

#### 主要方法

- `start_sol_session()`: 启动SOL会话
- `detect_tab_count()`: 检测TAB页数量
- `explore_all_tabs()`: 遍历所有TAB页
- `send_key(key)`: 发送按键到BIOS
- `read_screen_content()`: 读取屏幕内容
- `save_menu_structure()`: 保存菜单结构

#### 按键支持

```python
from bios_menu_explorer import KeyCode

KeyCode.UP      # 上箭头
KeyCode.DOWN    # 下箭头  
KeyCode.LEFT    # 左箭头
KeyCode.RIGHT   # 右箭头
KeyCode.ENTER   # 回车键
KeyCode.ESC     # ESC键
KeyCode.TAB     # TAB键
KeyCode.F1      # F1功能键
KeyCode.F10     # F10功能键
```

## 输出格式

### 菜单结构JSON

```json
{
  "Main": {
    "name": "Main",
    "path": "/Main", 
    "level": 0,
    "tab_index": 0,
    "parent_path": "",
    "children": [
      {
        "name": "System Information",
        "path": "/Main/System Information",
        "level": 1,
        "is_submenu": true,
        "children": [...]
      }
    ],
    "content": "...",
    "is_submenu": false
  }
}
```

### 菜单摘要JSON

```json
{
  "bios_info": {
    "total_tabs": 5,
    "exploration_time": "2024-01-01 12:00:00",
    "tabs": [
      {
        "name": "Main",
        "path": "/Main",
        "menu_count": 8,
        "menus": [...]
      }
    ]
  }
}
```

## 配置选项

### SOL连接配置

```python
SOL_CONFIG = {
    "host": "************",
    "username": "Administrator",
    "password": "Superuser9!",
    "interface": "lanplus",
    "timeout": 30
}
```

### 遍历设置

```python
EXPLORATION_CONFIG = {
    "max_depth": 5,              # 最大菜单深度
    "max_items_per_menu": 50,    # 每个菜单最大项目数
    "navigation_delay": 0.5,     # 导航延迟(秒)
    "similarity_threshold": 0.7   # 内容相似度阈值
}
```

## 使用场景

### 1. 完整BIOS结构分析

```python
# 获取完整的BIOS菜单结构
explorer = BIOSMenuExplorer(host, username, password)
structure = explorer.run_full_exploration()

# 分析结果
for tab_name, tab_data in structure.items():
    print(f"TAB: {tab_name}")
    print(f"子菜单数: {len(tab_data['children'])}")
```

### 2. 特定TAB遍历

```python
# 只遍历特定TAB
explorer.start_sol_session()
explorer.go_to_main_page()

# 切换到Advanced TAB
explorer.send_key(KeyCode.RIGHT)
tab_structure = explorer.explore_current_tab("Advanced", 1)
```

### 3. 手动导航控制

```python
# 手动控制导航
explorer.start_sol_session()

# 向下移动3步
for _ in range(3):
    explorer.send_key(KeyCode.DOWN)

# 进入子菜单
explorer.send_key(KeyCode.ENTER)

# 读取内容
content = explorer.read_screen_content()
```

## 注意事项

1. **网络连接**: 确保能够通过IPMI连接到目标主机
2. **BIOS状态**: 目标主机应处于BIOS界面
3. **权限要求**: 需要管理员权限进行SOL操作
4. **超时设置**: 根据网络延迟调整超时参数
5. **内容解析**: 不同BIOS厂商的界面格式可能需要调整解析规则

## 故障排除

### 常见问题

1. **SOL连接失败**
   - 检查网络连接和IPMI配置
   - 验证用户名密码
   - 确认目标主机IPMI服务正常

2. **菜单识别错误**
   - 调整`PARSING_RULES`中的正则表达式
   - 修改相似度阈值
   - 增加导航延迟时间

3. **遍历不完整**
   - 增加最大深度限制
   - 调整超时设置
   - 检查ESC键返回逻辑

### 调试模式

```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 保存中间结果
explorer.save_menu_structure(partial_structure, "debug_structure.json")
```

## 扩展开发

### 自定义解析规则

在`config.py`中修改`PARSING_RULES`来适配不同的BIOS界面：

```python
PARSING_RULES = {
    "menu_patterns": [
        r"your_custom_pattern",  # 添加自定义模式
    ],
    "tab_keywords": [
        "custom_tab_name",       # 添加自定义TAB名称
    ]
}
```

### 添加新的按键支持

```python
class KeyCode(Enum):
    CUSTOM_KEY = '\x1b[custom_sequence'
```

## 许可证

本项目基于现有的`simple_git_bios_info.py`代码开发，保持相同的使用条款。
