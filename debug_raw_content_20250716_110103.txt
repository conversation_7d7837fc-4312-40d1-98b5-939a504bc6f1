'\x1b[4;1H
\x1b[4;1H|  \x1b[1mAccess Level\x1b[4;29HAdministrator\x1b[0m\x1b[4;55HSet the Date. Use Tab   ^|
\x1b[5;1H|                                                   +|to switch between Date  *|
\x1b[6;1H|  \x1b[0mPlatform Information                           \x1b[0m  +|elements.               *|
\x1b[7;1H|  \x1b[1mPlatform\x1b[0m                 \x1b[1mTypeArcherCityRP\x1b[0m        +|Default Ranges:         *|
\x1b[8;1H|  \x1b[1mProcessor\x1b[8;29HC06F2 - EMR-SP Rx\x1b[0m       +|Year: 2010-2099         +|
\x1b[9;1H|  \x1b[1mPCH\x1b[0m                      \x1b[1mEBG A0/A1/B0/B1 SKU -\x1b[0m   +|Months: 1-12            v|
\x1b[10;1H|                           \x1b[1mB1\x1b[0m                      +|                         |
\x1b[11;1H|  \x1b[1mRC Revision\x1b[11;29H114.D30\x1b[12;1H\x1b[0m
\x1b[12;1H|  \x1b[1mBIOS ACM \x1b[0m                \x1b[1m1.1.A\x1b[0m                   *|><: Select Screen        |
\x1b[13;1H|  \x1b[1mSINIT ACM \x1b[13;29H1.1.A\x1b[14;1H\x1b[0m
\x1b[14;1H|                                                   *|Enter: Select            |
\x1b[15;1H|  \x1b[0mMemory Information                             
\x1b[16;1H\x1b[0m\x1b[16;1H|  \x1b[1mTotal Memory\x1b[0m             \x1b[1m524288 MB\x1b[0m               *|k/m: Scroll Help Area    |
\x1b[17;1H|  \x1b[1mSystem Memory Speed\x1b[0m      \x1b[1m4400 MT/s
\x1b[18;1H\x1b[0m\x1b[18;1H|                                                   *|F2: Previous Values      |
\x1b[19;1H|  System Language          [English]
\x1b[20;1H\x1b[20;1H|                                                   *|F4: Save & Exit          |
\x1b[21;1H|  \x1b[1mSystem Date\x1b[0m\x1b[21;29H[Wed \x1b[1m07\x1b[0m/16/2025]'