#!/usr/bin/env python3
"""
测试BIOS退出提示处理功能
专门测试"Exit Without Saving"等提示的智能处理
"""

from bios_menu_explorer import BIOSMenuExplorer, KeyCode
from config import SOL_CONFIG
import time


def test_exit_prompt_detection():
    """测试退出提示检测功能"""
    print("=== 测试退出提示检测功能 ===")
    
    explorer = BIOSMenuExplorer("test", "test", "test")
    
    # 测试不同的退出提示内容
    test_cases = [
        {
            "name": "标准退出提示",
            "content": "Exit Without Saving\nAre you sure you want to exit without saving changes?\nPress ESC to cancel, Enter to confirm",
            "expected": True
        },
        {
            "name": "带ANSI的退出提示",
            "content": "\x1b[1;37;41mExit Without Saving\x1b[0m\nDiscard changes and exit?",
            "expected": True
        },
        {
            "name": "保存退出提示",
            "content": "Save Changes and Exit\nDo you want to save configuration changes?",
            "expected": False  # 这个不是"Exit Without Saving"
        },
        {
            "name": "普通菜单内容",
            "content": "Main Menu\nSystem Information\nProcessor Information",
            "expected": False
        },
        {
            "name": "其他退出提示",
            "content": "Quit without saving changes\nAll modifications will be lost",
            "expected": True
        }
    ]
    
    print("测试退出提示检测:")
    for i, test_case in enumerate(test_cases, 1):
        result = explorer._check_exit_prompt(test_case["content"])
        status = "✅" if result == test_case["expected"] else "❌"
        print(f"  {i}. {test_case['name']}: {status} (期望: {test_case['expected']}, 实际: {result})")


def test_main_page_detection():
    """测试主页面检测功能"""
    print("\n=== 测试主页面检测功能 ===")
    
    explorer = BIOSMenuExplorer("test", "test", "test")
    
    test_cases = [
        {
            "name": "标准主页面",
            "content": "BIOS Setup Utility\nMain Advanced Security Boot\nSystem Information\nProcessor Information",
            "expected": True
        },
        {
            "name": "TAB导航页面",
            "content": "Main  Advanced  Platform Configuration  Socket Configuration  Server Mgmt  Security  Boot  Save & Exit",
            "expected": True
        },
        {
            "name": "子菜单页面",
            "content": "CPU Configuration\nCPU Speed: 2.4GHz\nCache Size: 8MB",
            "expected": False
        },
        {
            "name": "退出提示页面",
            "content": "Exit Without Saving\nAre you sure?",
            "expected": False
        }
    ]
    
    print("测试主页面检测:")
    for i, test_case in enumerate(test_cases, 1):
        result = explorer._is_main_page(test_case["content"])
        status = "✅" if result == test_case["expected"] else "❌"
        print(f"  {i}. {test_case['name']}: {status} (期望: {test_case['expected']}, 实际: {result})")


def test_prompt_handling():
    """测试提示处理功能"""
    print("\n=== 测试提示处理功能 ===")
    
    explorer = BIOSMenuExplorer("test", "test", "test")
    
    test_cases = [
        {
            "name": "退出提示",
            "content": "Exit Without Saving\nPress ESC to cancel",
            "expected_action": "esc"
        },
        {
            "name": "确认对话框",
            "content": "Are you sure you want to continue?\nPress Enter to confirm",
            "expected_action": "esc"  # 我们通常用ESC取消
        },
        {
            "name": "继续提示",
            "content": "Press any key to continue...",
            "expected_action": "enter"
        },
        {
            "name": "加载提示",
            "content": "Loading configuration, please wait...",
            "expected_action": "wait"
        },
        {
            "name": "普通内容",
            "content": "System Information\nCPU: Intel Core i7",
            "expected_action": None
        }
    ]
    
    print("测试提示处理:")
    for i, test_case in enumerate(test_cases, 1):
        # 由于handle_bios_prompts会实际发送按键，我们只测试检测部分
        result = explorer.handle_bios_prompts(test_case["content"])
        expected = test_case["expected_action"] is not None
        status = "✅" if result == expected else "❌"
        print(f"  {i}. {test_case['name']}: {status} (期望处理: {expected}, 实际处理: {result})")


def test_real_exit_scenario():
    """测试真实的退出场景处理"""
    print("\n=== 测试真实退出场景处理 ===")
    
    explorer = BIOSMenuExplorer(
        host=SOL_CONFIG['host'],
        username=SOL_CONFIG['username'],
        password=SOL_CONFIG['password']
    )
    
    try:
        print("启动SOL连接...")
        if not explorer.start_sol_session():
            print("❌ SOL连接失败")
            return
        
        print("✅ SOL连接成功")
        
        # 等待界面稳定
        time.sleep(3)
        
        print("测试回到主页面功能...")
        
        # 先深入几层菜单
        print("1. 尝试进入子菜单...")
        explorer.send_key(KeyCode.DOWN, 0.5)
        explorer.send_key(KeyCode.ENTER, 0.5)
        time.sleep(1)
        
        explorer.send_key(KeyCode.DOWN, 0.5)
        explorer.send_key(KeyCode.ENTER, 0.5)
        time.sleep(1)
        
        print("2. 现在尝试回到主页面...")
        
        # 读取当前内容
        before_content = explorer.read_screen_content()
        print(f"回到主页面前的内容长度: {len(before_content)}")
        
        # 使用优化后的go_to_main_page函数
        explorer.go_to_main_page()
        
        # 读取回到主页面后的内容
        after_content = explorer.read_screen_content()
        print(f"回到主页面后的内容长度: {len(after_content)}")
        
        # 验证是否在主页面
        if explorer._is_main_page(after_content):
            print("✅ 成功回到主页面")
        else:
            print("❌ 可能未成功回到主页面")
        
        # 检查是否还有退出提示
        if explorer._check_exit_prompt(after_content):
            print("⚠️  仍然检测到退出提示")
        else:
            print("✅ 没有检测到退出提示")
        
        # 保存调试信息
        with open("exit_test_before.txt", "w", encoding="utf-8") as f:
            f.write("回到主页面前的内容:\n")
            f.write("=" * 50 + "\n")
            f.write(before_content)
        
        with open("exit_test_after.txt", "w", encoding="utf-8") as f:
            f.write("回到主页面后的内容:\n")
            f.write("=" * 50 + "\n")
            f.write(after_content)
        
        print("调试信息已保存到 exit_test_before.txt 和 exit_test_after.txt")
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        explorer.close_sol_session()


def test_navigation_with_prompts():
    """测试带提示处理的导航"""
    print("\n=== 测试带提示处理的导航 ===")
    
    explorer = BIOSMenuExplorer(
        host=SOL_CONFIG['host'],
        username=SOL_CONFIG['username'],
        password=SOL_CONFIG['password']
    )
    
    try:
        if not explorer.start_sol_session():
            print("❌ SOL连接失败")
            return
        
        print("✅ SOL连接成功")
        time.sleep(3)
        
        # 确保在主页面
        explorer.go_to_main_page()
        time.sleep(1)
        
        print("测试菜单导航和提示处理...")
        
        # 尝试进入几个菜单项
        for i in range(3):
            print(f"\n--- 测试菜单项 {i+1} ---")
            
            # 向下移动
            explorer.send_key(KeyCode.DOWN, 0.3)
            
            # 尝试进入子菜单
            before_content = explorer.read_screen_content()
            success = explorer._try_enter_submenu()
            
            if success:
                print(f"✅ 成功进入菜单项 {i+1}")
                
                # 立即返回
                explorer.send_key(KeyCode.ESC, 0.5)
                time.sleep(0.5)
                
                # 处理可能的提示
                after_content = explorer.read_screen_content()
                explorer.handle_bios_prompts(after_content)
                
            else:
                print(f"❌ 未能进入菜单项 {i+1}")
        
        print("\n导航测试完成")
        
    except Exception as e:
        print(f"❌ 导航测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        explorer.close_sol_session()


def main():
    """主测试函数"""
    print("BIOS退出提示处理测试")
    print("=" * 40)
    
    # 运行离线测试
    test_exit_prompt_detection()
    test_main_page_detection()
    test_prompt_handling()
    
    # 询问是否运行真实连接测试
    response = input("\n是否运行真实BIOS连接测试? (y/n): ").strip().lower()
    if response == 'y':
        test_real_exit_scenario()
        
        response2 = input("\n是否测试带提示处理的导航? (y/n): ").strip().lower()
        if response2 == 'y':
            test_navigation_with_prompts()
    
    print("\n测试完成！")
    print("\n主要改进:")
    print("✅ 智能检测 'Exit Without Saving' 提示")
    print("✅ 自动按ESC消除退出提示")
    print("✅ 检测是否已在主页面")
    print("✅ 处理各种BIOS提示和对话框")
    print("✅ 改进的菜单导航逻辑")


if __name__ == "__main__":
    main()
