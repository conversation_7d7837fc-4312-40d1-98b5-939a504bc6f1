# BIOS TAB检测功能使用指南

## 概述

基于您的需求，我已经改进了BIOS TAB名称检测功能，现在能够：

1. **处理ANSI转义字符** - 自动清理BIOS输出中的ANSI控制序列
2. **智能TAB识别** - 支持多种BIOS TAB显示格式
3. **准确定位当前TAB** - 根据高亮、方括号等标识确定当前激活的TAB

## 主要改进

### 1. ANSI转义字符处理

```python
def clean_ansi(text):
    # 移除ANSI转义序列
    ansi_escape = re.compile(r'\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])')
    return ansi_escape.sub('', text)
```

### 2. 多格式TAB识别

支持的TAB显示格式：
- **高亮格式**: `[Main] Advanced Security Boot`
- **ANSI格式**: `\x1b[1;37;44m Main \x1b[0;37;40m Advanced`
- **竖线分隔**: `Main | Advanced | Security`
- **标准格式**: `Main  Advanced  Security  Boot`

### 3. 智能TAB名称提取

```python
# 支持的TAB关键词（已根据您的修改更新）
tab_keywords = [
    'main', 'advanced', 'platform configuration', 
    'socket configuration', 'server mgmt', 'security', 
    'boot', 'save & exit'
]
```

## 使用方法

### 1. 基本TAB检测

```python
from bios_menu_explorer import BIOSMenuExplorer

explorer = BIOSMenuExplorer(host, username, password)
explorer.start_sol_session()

# 读取屏幕内容
content = explorer.read_screen_content()

# 提取TAB名称
tab_name = explorer._extract_tab_name(content, 0)
print(f"当前TAB: {tab_name}")
```

### 2. 调试TAB检测

```python
# 启用详细调试
explorer.debug_screen_content(content, save_to_file=True)

# 这会生成以下调试文件：
# - debug_raw_content.txt (原始内容)
# - debug_clean_content.txt (清理后内容)
```

### 3. 运行调试脚本

```bash
# 测试TAB名称提取算法
python debug_tab_detection.py

# 测试真实BIOS连接
python test_real_bios.py
```

## 调试输出示例

当运行TAB检测时，您会看到类似的调试信息：

```
=== 调试TAB 1 内容 ===
原始内容长度: 1024
发现 15 个ANSI转义序列:
  '\x1b[2J'
  '\x1b[H'
  '\x1b[1;37;44m'
  '\x1b[0;37;40m'

清理后内容长度: 856

前20行内容分析:
  行 1: 'BIOS Setup Utility'
  行 2: ' Main  Advanced  Platform Configuration  Socket Configuration'
  行 3: 'System Information'

可能的TAB标识:
  行2: 发现关键词 'main' -> ' Main  Advanced  Platform Configuration'
  行2: 发现关键词 'advanced' -> ' Main  Advanced  Platform Configuration'

在第2行发现可能的TAB导航行: Main  Advanced  Platform Configuration
解析出的TAB名称: ['Main', 'Advanced', 'Platform Configuration']
识别的TAB名称: Main
```

## 测试结果

改进后的TAB检测功能测试结果：

| 测试用例 | 格式 | 结果 |
|---------|------|------|
| 高亮格式 | `[Main] Advanced` | ✅ 成功 |
| ANSI格式 | `\x1b[1;37;44m Main \x1b[0;37;40m` | ✅ 成功 |
| 竖线分隔 | `Main \| Advanced` | ✅ 成功 |
| 标准格式 | `Main Advanced Security` | ✅ 成功 |

## 故障排除

### 1. TAB名称识别失败

如果TAB名称识别失败，检查：

```python
# 查看原始内容
with open("debug_raw_content.txt", "r") as f:
    raw_content = f.read()
    print("原始内容:", repr(raw_content[:200]))

# 查看清理后内容
with open("debug_clean_content.txt", "r") as f:
    clean_content = f.read()
    print("清理后内容:", clean_content[:200])
```

### 2. 添加新的TAB关键词

如果您的BIOS有其他TAB名称，可以在`config.py`中添加：

```python
PARSING_RULES = {
    "tab_keywords": [
        "main", "advanced", "platform configuration", 
        "socket configuration", "server mgmt", "security", 
        "boot", "save & exit",
        "your_new_tab_name"  # 添加新的TAB名称
    ]
}
```

### 3. 调整识别模式

如果需要支持新的TAB显示格式，可以修改`_extract_tab_name`函数中的模式：

```python
# 在find_active_tab函数中添加新模式
patterns = [
    r'\[([^\]]+)\]',  # 方括号包围
    r'>\s*([^<\n]+?)\s*<',  # 尖括号包围
    r'\*\s*([^\*\n]+?)\s*\*',  # 星号包围
    r'your_new_pattern',  # 添加新模式
]
```

## 实际使用示例

### 完整的TAB遍历

```python
# 使用改进的TAB检测进行完整遍历
explorer = BIOSMenuExplorer(host, username, password)

# 运行单个TAB遍历（用于调试）
structure = explorer.run_full_exploration()

# 检查结果
for tab_name, tab_data in structure.items():
    print(f"TAB: {tab_name}")
    print(f"  路径: {tab_data['path']}")
    print(f"  子菜单: {len(tab_data['children'])}")
```

### 手动TAB切换

```python
# 手动控制TAB切换
explorer.start_sol_session()
explorer.go_to_main_page()

# 读取当前TAB
content = explorer.read_screen_content()
current_tab = explorer._extract_tab_name(content, 0)
print(f"当前TAB: {current_tab}")

# 切换到下一个TAB
explorer.send_key(KeyCode.RIGHT)
time.sleep(1)

# 读取新TAB
new_content = explorer.read_screen_content()
new_tab = explorer._extract_tab_name(new_content, 1)
print(f"新TAB: {new_tab}")
```

## 注意事项

1. **BIOS界面稳定性** - 确保在读取内容前等待界面稳定
2. **网络延迟** - 根据网络情况调整读取超时时间
3. **BIOS版本差异** - 不同厂商的BIOS可能需要调整识别规则
4. **调试文件** - 保存调试文件有助于分析和改进识别算法

## 下一步

1. 运行 `python test_real_bios.py` 测试真实BIOS连接
2. 检查生成的调试文件确认TAB识别是否正确
3. 根据实际BIOS界面调整识别规则
4. 使用改进的功能进行完整的BIOS菜单遍历
