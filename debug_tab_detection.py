#!/usr/bin/env python3
"""
TAB检测调试脚本
专门用于调试和测试BIOS TAB名称识别功能
"""

from bios_menu_explorer import BIOSMenuExplorer, KeyCode
from config import SOL_CONFIG
import time
import re


def test_ansi_cleaning():
    """测试ANSI转义字符清理功能"""
    print("=== 测试ANSI转义字符清理 ===")
    
    # 模拟包含ANSI转义字符的BIOS内容
    test_content = """
\x1b[2J\x1b[H\x1b[0;37;40mBIOS Setup Utility
\x1b[1;37;44m Main \x1b[0;37;40m Advanced \x1b[0;37;40m Platform Configuration \x1b[0;37;40m Socket Configuration \x1b[0;37;40m Server Mgmt \x1b[0;37;40m Security \x1b[0;37;40m Boot \x1b[0;37;40m Save & Exit
\x1b[3;1H\x1b[0;37;40mSystem Information
\x1b[4;1H\x1b[0;37;40mProcessor Information
"""
    
    # 清理ANSI转义序列
    ansi_escape = re.compile(r'\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])')
    clean_content = ansi_escape.sub('', test_content)
    
    print("原始内容:")
    print(repr(test_content))
    print("\n清理后内容:")
    print(repr(clean_content))
    print("\n清理后可读内容:")
    print(clean_content)
    
    return clean_content


def test_tab_extraction():
    """测试TAB名称提取功能"""
    print("\n=== 测试TAB名称提取 ===")
    
    # 创建测试用的遍历器
    explorer = BIOSMenuExplorer("test", "test", "test")
    
    # 测试不同格式的BIOS内容
    test_cases = [
        {
            "name": "标准格式",
            "content": "BIOS Setup Utility\n Main  Advanced  Platform Configuration  Socket Configuration  Server Mgmt  Security  Boot  Save & Exit\nSystem Information",
            "expected": "Main"
        },
        {
            "name": "完整标准格式",
            "content": "BIOS Setup Utility\nMain Advanced Platform Configuration Socket Configuration Server Mgmt Security Boot Save & Exit\nSystem Information\nProcessor Information",
            "expected": "Main"
        },
        {
            "name": "高亮格式",
            "content": "BIOS Setup Utility\n[Main] Advanced Platform Configuration Socket Configuration Server Mgmt Security Boot Save & Exit\nSystem Information",
            "expected": "Main"
        },
        {
            "name": "带ANSI的格式",
            "content": "\x1b[1;37;44m Main \x1b[0;37;40m Advanced \x1b[0;37;40m Platform Configuration",
            "expected": "Main"
        },
        {
            "name": "竖线分隔格式",
            "content": "Main | Advanced | Platform Configuration | Socket Configuration",
            "expected": "Main"
        }
    ]
    
    for i, test_case in enumerate(test_cases):
        print(f"\n测试用例 {i+1}: {test_case['name']}")
        print(f"输入内容: {repr(test_case['content'][:100])}")
        
        extracted_name = explorer._extract_tab_name(test_case['content'], 0)
        print(f"提取结果: {extracted_name}")
        print(f"期望结果: {test_case['expected']}")
        print(f"匹配: {'✓' if test_case['expected'].lower() in extracted_name.lower() else '✗'}")


def debug_real_bios_connection():
    """调试真实BIOS连接的TAB检测"""
    print("\n=== 调试真实BIOS连接 ===")
    
    explorer = BIOSMenuExplorer(
        host=SOL_CONFIG['host'],
        username=SOL_CONFIG['username'],
        password=SOL_CONFIG['password']
    )
    
    try:
        print("启动SOL连接...")
        if not explorer.start_sol_session():
            print("SOL连接失败")
            return
        
        print("SOL连接成功，等待界面稳定...")
        time.sleep(3)
        
        # 确保在主页面
        explorer.go_to_main_page()
        time.sleep(2)
        
        print("读取当前屏幕内容...")
        content = explorer.read_screen_content(timeout=3.0)
        
        print(f"读取到内容长度: {len(content)}")
        
        # 调试屏幕内容
        explorer.debug_screen_content(content, save_to_file=True)
        
        # 测试TAB名称提取
        tab_name = explorer._extract_tab_name(content, 0)
        print(f"\n最终识别的TAB名称: {tab_name}")
        
        # 尝试切换TAB并检测
        print("\n尝试切换到下一个TAB...")
        explorer.send_key(KeyCode.RIGHT, delay=1.0)
        time.sleep(2)
        
        content2 = explorer.read_screen_content(timeout=3.0)
        tab_name2 = explorer._extract_tab_name(content2, 1)
        print(f"第二个TAB名称: {tab_name2}")
        
        # 保存两个TAB的内容用于对比
        with open("tab1_content.txt", "w", encoding="utf-8") as f:
            f.write(content)
        with open("tab2_content.txt", "w", encoding="utf-8") as f:
            f.write(content2)
        
        print("TAB内容已保存到 tab1_content.txt 和 tab2_content.txt")
        
    except Exception as e:
        print(f"调试过程中出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        explorer.close_sol_session()


def analyze_saved_content():
    """分析已保存的内容文件"""
    print("\n=== 分析已保存的内容文件 ===")
    
    files_to_analyze = [
        "debug_raw_content.txt",
        "debug_clean_content.txt", 
        "tab1_content.txt",
        "tab2_content.txt"
    ]
    
    for filename in files_to_analyze:
        try:
            with open(filename, "r", encoding="utf-8") as f:
                content = f.read()
            
            print(f"\n分析文件: {filename}")
            print(f"文件大小: {len(content)} 字符")
            
            # 查找TAB相关的行
            lines = content.split('\n')
            tab_keywords = ['main', 'advanced', 'platform configuration', 'socket configuration', 
                           'server mgmt', 'security', 'boot', 'save & exit']
            
            print("包含TAB关键词的行:")
            for i, line in enumerate(lines[:20]):
                line_lower = line.lower()
                for keyword in tab_keywords:
                    if keyword in line_lower:
                        print(f"  行{i+1}: {repr(line.strip())}")
                        break
            
        except FileNotFoundError:
            print(f"文件 {filename} 不存在")
        except Exception as e:
            print(f"分析文件 {filename} 时出错: {e}")


def interactive_debug():
    """交互式调试模式"""
    print("\n=== 交互式调试模式 ===")
    
    while True:
        print("\n选择调试选项:")
        print("1. 测试ANSI字符清理")
        print("2. 测试TAB名称提取")
        print("3. 调试真实BIOS连接")
        print("4. 分析已保存的内容")
        print("5. 退出")
        
        choice = input("请输入选择 (1-5): ").strip()
        
        if choice == "1":
            test_ansi_cleaning()
        elif choice == "2":
            test_tab_extraction()
        elif choice == "3":
            debug_real_bios_connection()
        elif choice == "4":
            analyze_saved_content()
        elif choice == "5":
            print("退出调试模式")
            break
        else:
            print("无效选择，请重试")


def main():
    """主函数"""
    print("BIOS TAB检测调试工具")
    print("=" * 40)
    
    # 运行所有测试
    test_ansi_cleaning()
    test_tab_extraction()
    
    # 询问是否进行真实连接测试
    response = input("\n是否进行真实BIOS连接测试? (y/n): ").strip().lower()
    if response == 'y':
        debug_real_bios_connection()
    
    # 询问是否进入交互模式
    response = input("\n是否进入交互式调试模式? (y/n): ").strip().lower()
    if response == 'y':
        interactive_debug()
    
    print("\n调试完成")


if __name__ == "__main__":
    main()
