import re


# tab_line =  '\x1b[1;1H\x1b[1m\x1b[1;1H                               Aptio Setup - AMI                                \x1b[2;1H\x1b[0m  Main \x1b[0m Advanced \x1b[0m  Platform Configuration  Socket Configuration  Server Mgmt  >\x1b[3;1H\x1b[0m/----------------------------------------------------+-------------------------\\\x1b[4;1H|  \x1b[0mBIOS Information                               \x1b[0m  ^|                         |\x1b[5;1H|  \x1b[1mBIOS Version\x1b[0m             \x1b[1m04.25.02.20\x1b[0m             *|                         |\x1b[6;1H|  \x1b[1mBuild Date\x1b[0m               \x1b[1m06/30/2025\x1b[0m              *|                         |\x1b[7;1H|                                                   *|                         |\x1b[8;1H|  \x1b[1mDebug Mode Status\x1b[0m        \x1b[1mDisabled\x1b[0m                *|                         |\x1b[9;1H|                                                   *|                         |\x1b[10;1H|  \x1b[1mProduct Name\x1b[0m             \x1b[1mR5300 G5\x1b[0m                *|                         |\x1b[11;1H|  \x1b[1mSerial Number\x1b[0m            \x1b[1mRcdTAu1nTm6tOOBMncCwhLM\x1b[0m *|-------------------------|\x1b[12;1H|                           \x1b[1mbptXKey6eBMJrUjdaZt2Yen\x1b[0m *|><: Select Screen        |\x1b[13;1H|                           \x1b[1mguox1BrXeKKbZYpLl\x1b[0m       *|^v: Select Item          |\x1b[14;1H|  \x1b[1mAsset Tag\x1b[0m                \x1b[1mMI05102A\x1b[0m                *|Enter: Select            |\x1b[15;1H|  \x1b[1mAccess Level\x1b[0m             \x1b[1mAdministrator\x1b[0m           +|+/-: Change Option       |\x1b[16;1H|                                                   +|k/m: Scroll Help Area    |\x1b[17;1H|  \x1b[0mPlatform Information                           \x1b[0m  +|F1: General Help         |\x1b[18;1H|  \x1b[1mPlatform\x1b[0m                 \x1b[1mTypeArcherCityRP\x1b[0m        +|F2: Previous Values      |\x1b[19;1H|  \x1b[1mProcessor\x1b[0m                \x1b[1mC06F2 - EMR-SP Rx\x1b[0m       +|F3: Optimized Defaults   |\x1b[20;1H|  \x1b[1mPCH\x1b[0m                      \x1b[1mEBG A0/A1/B0/B1 SKU -\x1b[0m   +|F4: Save & Exit          |\x1b[21;1H|                           \x1b[1mB1\x1b[0m                      v|F7: Search Setup Items   |\x1b[22;1H|                                                    |ESC: Exit                |\x1b[23;1H\\----------------------------------------------------+-------------------------/\x1b[24;1H\x1b[0m                    Version 2.22.1290 Copyright (C) 2025 AMI                   '

tab_line = '\x1b[1;1H\x1b[1m\x1b[1;1H                               Aptio Setup - AMI                                \x1b[2;1H\x1b[0m    Main  Advanced \x1b[0m Platform Configuration \x1b[0m Socket Configuration  Server Mgmt  >\x1b[3;1H\x1b[0m/----------------------------------------------------+-------------------------\\\x1b[4;1H|> PCH-IO Configuration                              |                         |\x1b[5;1H|\x1b[1m> Miscellaneous Configuration\x1b[0m                       |                         |\x1b[6;1H|> Server ME Configuration                           |                         |\x1b[7;1H|> Server ME Debug Configuration                     |                         |\x1b[8;1H|> Runtime Error Logging                             |                         |\x1b[9;1H|                                                    |                         |\x1b[10;1H|  \x1b[0m-----------------------------------------------\x1b[0m   |                         |\x1b[11;1H|  \x1b[0mSetup Warning:                                 \x1b[0m   |-------------------------|\x1b[12;1H|  \x1b[0mSetting items on this Screen to incorrect      \x1b[0m   |><: Select Screen        |\x1b[13;1H|  \x1b[0mvalues                                         \x1b[0m   |^v: Select Item          |\x1b[14;1H|  \x1b[0mmay cause system to malfunction!               \x1b[0m   |Enter: Select            |\x1b[15;1H|                                                    |+/-: Change Option       |\x1b[16;1H|                                                    |k/m: Scroll Help Area    |\x1b[17;1H|                                                    |F1: General Help         |\x1b[18;1H|                                                    |F2: Previous Values      |\x1b[19;1H|                                                    |F3: Optimized Defaults   |\x1b[20;1H|                                                    |F4: Save & Exit          |\x1b[21;1H|                                                    |F7: Search Setup Items   |\x1b[22;1H|                                                    |ESC: Exit                |\x1b[23;1H\\----------------------------------------------------+-------------------------/\x1b[24;1H\x1b[0m                    Version 2.22.1290 Copyright (C) 2025 AMI                   '

pattern1 = r'\x1b\[2;1H\x1b\[0m\s*.*?\x1b\[0m\s*([^\x1b]+?)\s*\x1b\[0m'
matches1 = re.findall(pattern1, tab_line)
print(matches1)


