# BIOS退出提示处理优化

## 概述

根据您的需求，我已经优化了BIOS菜单遍历器的逻辑，特别是对"Exit Without Saving"提示的智能处理。当出现此类提示时，系统会识别这表示已经到达主菜单，只需按ESC键消除提示即可。

## 主要优化

### 1. 智能退出提示检测

```python
def _check_exit_prompt(self, content: str) -> bool:
    """检查是否出现退出提示"""
    # 支持的退出提示类型
    exit_indicators = [
        'exit without saving',
        'exit discarding changes', 
        'discard changes and exit',
        'quit without saving',
        'exit setup without saving'
    ]
```

**特点：**
- ✅ 自动清理ANSI转义字符
- ✅ 支持多种退出提示格式
- ✅ 大小写不敏感匹配

### 2. 优化的主页面检测

```python
def _is_main_page(self, content: str) -> bool:
    """检查是否在主页面"""
    # TAB导航行检测（包含多个TAB关键词）
    keyword_count = sum(1 for keyword in tab_keywords if keyword in clean_content)
    if keyword_count >= 3:  # 如果包含3个或更多TAB关键词
        return True
```

**检测标准：**
- 包含3个或更多TAB关键词 → 确认为主页面
- 包含"System Information"等主页面特征
- 包含"BIOS Setup Utility"标题

### 3. 智能回到主页面逻辑

```python
def go_to_main_page(self):
    """回到BIOS主页面，智能处理退出提示"""
    for attempt in range(max_attempts):
        content = self.read_screen_content()
        
        if self._check_exit_prompt(content):
            print("检测到退出提示，按ESC消除提示")
            self.send_key(KeyCode.ESC, 0.5)
            # 验证是否成功回到主页面
            
        elif self._is_main_page(content):
            print("✅ 已在主页面")
            break
        else:
            # 继续按ESC返回上级
            self.send_key(KeyCode.ESC, 0.3)
```

**优化逻辑：**
1. **检测退出提示** → 按ESC消除 → 验证结果
2. **检测主页面** → 确认完成
3. **其他情况** → 继续按ESC返回上级

### 4. 通用提示处理系统

```python
def handle_bios_prompts(self, content: str) -> bool:
    """处理各种BIOS提示和对话框"""
    prompt_handlers = [
        {
            'indicators': ['exit without saving', 'discard changes'],
            'action': 'esc',
            'description': '退出提示'
        },
        {
            'indicators': ['are you sure', 'confirm', 'warning'],
            'action': 'esc', 
            'description': '确认对话框'
        },
        {
            'indicators': ['press any key', 'continue'],
            'action': 'enter',
            'description': '继续提示'
        }
    ]
```

**支持的提示类型：**
- 🚪 **退出提示** → ESC键取消
- ⚠️ **确认对话框** → ESC键取消
- ⏯️ **继续提示** → Enter键继续
- ⏳ **加载提示** → 等待完成

### 5. 增强的子菜单进入逻辑

```python
def _try_enter_submenu(self) -> bool:
    """尝试进入子菜单，智能处理各种提示"""
    # 发送回车键
    self.send_key(KeyCode.ENTER)
    
    # 检查并处理可能出现的提示
    for attempt in range(max_prompt_attempts):
        if self.handle_bios_prompts(after_content):
            # 处理提示后重新读取内容
            after_content = self.read_screen_content()
        else:
            break
```

## 测试结果

### 离线测试结果

```
=== 测试退出提示检测功能 ===
✅ 标准退出提示: 正确识别
✅ 带ANSI的退出提示: 正确识别  
✅ 保存退出提示: 正确区分
✅ 普通菜单内容: 正确排除
✅ 其他退出提示: 正确识别

=== 测试主页面检测功能 ===
✅ 标准主页面: 正确识别
✅ TAB导航页面: 正确识别
✅ 子菜单页面: 正确排除
✅ 退出提示页面: 正确排除

=== 测试提示处理功能 ===
✅ 退出提示: 正确处理 (ESC)
✅ 确认对话框: 正确处理 (ESC)
✅ 继续提示: 正确处理 (Enter)
✅ 加载提示: 正确处理 (等待)
✅ 普通内容: 正确忽略
```

## 使用方法

### 1. 基本使用

```python
from bios_menu_explorer import BIOSMenuExplorer

explorer = BIOSMenuExplorer(host, username, password)
explorer.start_sol_session()

# 智能回到主页面（自动处理退出提示）
explorer.go_to_main_page()

# 验证是否在主页面
content = explorer.read_screen_content()
if explorer._is_main_page(content):
    print("✅ 在主页面")
```

### 2. 手动提示处理

```python
# 读取屏幕内容
content = explorer.read_screen_content()

# 检查并处理提示
if explorer.handle_bios_prompts(content):
    print("已处理BIOS提示")

# 检查特定的退出提示
if explorer._check_exit_prompt(content):
    print("检测到退出提示")
    explorer.send_key(KeyCode.ESC)
```

### 3. 运行测试

```bash
# 测试退出提示处理功能
python test_exit_prompt_handling.py

# 运行优化后的遍历示例
python example_usage.py
```

## 实际应用场景

### 场景1: 深层菜单返回主页面

```
用户操作: 进入多层子菜单
系统状态: Advanced → CPU Config → Power Settings
按ESC返回: Power Settings → CPU Config → Advanced → 出现"Exit Without Saving"
优化处理: 检测到退出提示 → 按ESC消除 → 确认在主页面 ✅
```

### 场景2: 意外触发退出

```
用户操作: 在主页面误按ESC
系统状态: 显示"Exit Without Saving"提示
优化处理: 检测到退出提示 → 按ESC取消 → 保持在主页面 ✅
```

### 场景3: 菜单导航中的确认对话框

```
用户操作: 尝试进入某个配置项
系统状态: 显示"Are you sure?"确认对话框
优化处理: 检测到确认对话框 → 按ESC取消 → 继续导航 ✅
```

## 配置选项

### 自定义退出提示关键词

```python
# 在_check_exit_prompt函数中添加新的关键词
exit_indicators = [
    'exit without saving',
    'your_custom_exit_prompt',  # 添加自定义提示
]
```

### 调整检测敏感度

```python
# 在_is_main_page函数中调整TAB关键词数量阈值
if keyword_count >= 3:  # 调整这个数值
    return True
```

## 调试和故障排除

### 1. 启用详细调试

```python
# 保存屏幕内容用于分析
explorer.debug_screen_content(content, save_to_file=True)

# 检查生成的调试文件
# - debug_raw_content.txt
# - debug_clean_content.txt
```

### 2. 测试特定提示

```python
# 测试退出提示检测
test_content = "Exit Without Saving\nAre you sure?"
result = explorer._check_exit_prompt(test_content)
print(f"检测结果: {result}")
```

### 3. 验证主页面检测

```python
# 测试主页面检测
test_content = "Main Advanced Security Boot\nSystem Information"
result = explorer._is_main_page(test_content)
print(f"主页面检测: {result}")
```

## 总结

这次优化显著改进了BIOS菜单遍历器的鲁棒性：

1. **智能退出处理** - 自动识别并处理"Exit Without Saving"提示
2. **准确主页面检测** - 多重标准确保正确识别主页面
3. **通用提示系统** - 处理各种BIOS对话框和提示
4. **增强的导航逻辑** - 更可靠的菜单遍历过程
5. **完善的测试覆盖** - 全面的测试确保功能正确性

现在系统能够智能地处理BIOS中的各种提示，确保遍历过程的稳定性和可靠性。
