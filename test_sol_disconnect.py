#!/usr/bin/env python3
"""
测试SOL断开功能
验证SOL连接的正确断开和deactivate命令
"""

from bios_menu_explorer import BIOSMenuExplorer
from config import SOL_CONFIG
import time
import subprocess
import signal
import sys


def test_normal_sol_disconnect():
    """测试正常的SOL断开"""
    print("=== 测试正常SOL断开 ===")
    
    explorer = BIOSMenuExplorer(
        host=SOL_CONFIG['host'],
        username=SOL_CONFIG['username'],
        password=SOL_CONFIG['password']
    )
    
    try:
        print("1. 启动SOL连接...")
        if not explorer.start_sol_session():
            print("❌ SOL连接失败")
            return False
        
        print("✅ SOL连接成功")
        
        # 等待一段时间
        print("2. 等待5秒...")
        time.sleep(5)
        
        # 读取一些内容
        print("3. 读取屏幕内容...")
        content = explorer.read_screen_content()
        print(f"   读取到 {len(content)} 字符")
        
        # 正常关闭
        print("4. 正常关闭SOL连接...")
        explorer.close_sol_session()
        
        print("✅ 正常断开测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 正常断开测试失败: {e}")
        return False


def test_force_sol_disconnect():
    """测试强制SOL断开"""
    print("\n=== 测试强制SOL断开 ===")
    
    explorer = BIOSMenuExplorer(
        host=SOL_CONFIG['host'],
        username=SOL_CONFIG['username'],
        password=SOL_CONFIG['password']
    )
    
    try:
        print("1. 启动SOL连接...")
        if not explorer.start_sol_session():
            print("❌ SOL连接失败")
            return False
        
        print("✅ SOL连接成功")
        
        # 等待一段时间
        print("2. 等待3秒...")
        time.sleep(3)
        
        # 强制断开
        print("3. 强制断开SOL连接...")
        explorer.force_disconnect_sol()
        
        print("✅ 强制断开测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 强制断开测试失败: {e}")
        return False


def test_sol_deactivate_command():
    """测试SOL deactivate命令"""
    print("\n=== 测试SOL deactivate命令 ===")
    
    explorer = BIOSMenuExplorer(
        host=SOL_CONFIG['host'],
        username=SOL_CONFIG['username'],
        password=SOL_CONFIG['password']
    )
    
    try:
        print("1. 启动SOL连接...")
        if not explorer.start_sol_session():
            print("❌ SOL连接失败")
            return False
        
        print("✅ SOL连接成功")
        
        # 等待一段时间
        print("2. 等待3秒...")
        time.sleep(3)
        
        # 测试deactivate命令
        print("3. 执行SOL deactivate命令...")
        explorer.deactivate_sol()
        
        # 检查进程状态
        print("4. 检查SOL进程状态...")
        if explorer.sol_process:
            if explorer.sol_process.poll() is None:
                print("   SOL进程仍在运行")
            else:
                print("   SOL进程已结束")
        else:
            print("   SOL进程对象为空")
        
        # 清理
        explorer.close_sol_session()
        
        print("✅ deactivate命令测试完成")
        return True
        
    except Exception as e:
        print(f"❌ deactivate命令测试失败: {e}")
        return False


def test_interrupt_handling():
    """测试中断处理"""
    print("\n=== 测试中断处理 ===")
    
    explorer = BIOSMenuExplorer(
        host=SOL_CONFIG['host'],
        username=SOL_CONFIG['username'],
        password=SOL_CONFIG['password']
    )
    
    def signal_handler(signum, frame):
        print(f"\n收到信号 {signum}，正在清理...")
        explorer.force_disconnect_sol()
        sys.exit(0)
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        print("1. 启动SOL连接...")
        if not explorer.start_sol_session():
            print("❌ SOL连接失败")
            return False
        
        print("✅ SOL连接成功")
        print("2. 等待10秒（可以按Ctrl+C测试中断处理）...")
        
        for i in range(10):
            print(f"   等待中... {i+1}/10")
            time.sleep(1)
        
        # 正常结束
        explorer.close_sol_session()
        
        print("✅ 中断处理测试完成")
        return True
        
    except KeyboardInterrupt:
        print("\n收到键盘中断")
        explorer.force_disconnect_sol()
        return True
    except Exception as e:
        print(f"❌ 中断处理测试失败: {e}")
        return False


def test_multiple_connections():
    """测试多次连接和断开"""
    print("\n=== 测试多次连接断开 ===")
    
    success_count = 0
    total_tests = 3
    
    for i in range(total_tests):
        print(f"\n--- 第 {i+1} 次连接测试 ---")
        
        explorer = BIOSMenuExplorer(
            host=SOL_CONFIG['host'],
            username=SOL_CONFIG['username'],
            password=SOL_CONFIG['password']
        )
        
        try:
            # 连接
            if not explorer.start_sol_session():
                print(f"❌ 第 {i+1} 次连接失败")
                continue
            
            print(f"✅ 第 {i+1} 次连接成功")
            
            # 短暂使用
            time.sleep(2)
            content = explorer.read_screen_content()
            print(f"   读取到 {len(content)} 字符")
            
            # 断开
            explorer.close_sol_session()
            print(f"✅ 第 {i+1} 次断开成功")
            
            success_count += 1
            
            # 等待一下再进行下次连接
            time.sleep(1)
            
        except Exception as e:
            print(f"❌ 第 {i+1} 次测试失败: {e}")
    
    print(f"\n多次连接测试结果: {success_count}/{total_tests} 成功")
    return success_count == total_tests


def check_sol_status():
    """检查SOL状态"""
    print("\n=== 检查SOL状态 ===")
    
    try:
        # 检查是否有活动的SOL会话
        cmd = ['ipmitool', '-I', 'lanplus', '-H', SOL_CONFIG['host'],
               '-U', SOL_CONFIG['username'], '-P', SOL_CONFIG['password'],
               'sol', 'info']
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
        
        print("SOL状态信息:")
        if result.returncode == 0:
            print(result.stdout)
        else:
            print(f"获取SOL状态失败: {result.stderr}")
            
    except Exception as e:
        print(f"检查SOL状态时出错: {e}")


def main():
    """主测试函数"""
    print("SOL断开功能测试")
    print("=" * 40)
    
    # 检查初始SOL状态
    check_sol_status()
    
    # 运行测试
    tests = [
        ("正常SOL断开", test_normal_sol_disconnect),
        ("强制SOL断开", test_force_sol_disconnect),
        ("SOL deactivate命令", test_sol_deactivate_command),
        ("多次连接断开", test_multiple_connections),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results[test_name] = result
            status = "✅ 成功" if result else "❌ 失败"
            print(f"{test_name}: {status}")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            results[test_name] = False
        
        # 测试间隔
        time.sleep(2)
    
    # 最终检查SOL状态
    check_sol_status()
    
    # 显示总结
    print(f"\n{'='*20} 测试总结 {'='*20}")
    success_count = sum(1 for result in results.values() if result)
    total_count = len(results)
    
    for test_name, result in results.items():
        status = "✅ 成功" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n总体结果: {success_count}/{total_count} 测试通过")
    
    if success_count == total_count:
        print("🎉 所有SOL断开功能测试通过！")
    else:
        print("⚠️  部分测试失败，请检查SOL连接配置")
    
    # 询问是否测试中断处理
    response = input("\n是否测试中断处理功能? (y/n): ").strip().lower()
    if response == 'y':
        test_interrupt_handling()


if __name__ == "__main__":
    main()
