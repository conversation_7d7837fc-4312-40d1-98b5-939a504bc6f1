#!/usr/bin/env python3
"""
SOL断开功能使用示例
展示如何正确使用SOL连接和断开功能
"""

from bios_menu_explorer import BIOSMenuExplorer
from config import SOL_CONFIG
import time
import signal
import sys


def basic_usage_example():
    """基本使用示例"""
    print("=== 基本SOL使用示例 ===")
    
    explorer = BIOSMenuExplorer(
        host=SOL_CONFIG['host'],
        username=SOL_CONFIG['username'],
        password=SOL_CONFIG['password']
    )
    
    try:
        # 1. 启动SOL连接
        print("1. 启动SOL连接...")
        if not explorer.start_sol_session():
            print("❌ SOL连接失败")
            return
        
        print("✅ SOL连接成功")
        
        # 2. 执行一些操作
        print("2. 执行BIOS操作...")
        
        # 回到主页面
        main_content = explorer.go_to_main_page()
        print(f"   获取主页面内容: {len(main_content)} 字符")
        
        # 提取TAB名称
        tab_name = explorer._extract_tab_name(main_content, 0)
        print(f"   当前TAB: {tab_name}")
        
        # 3. 正常关闭SOL连接
        print("3. 正常关闭SOL连接...")
        explorer.close_sol_session()
        print("✅ SOL连接已正常关闭")
        
    except Exception as e:
        print(f"❌ 操作过程中出错: {e}")
        # 出错时强制断开
        print("尝试强制断开SOL连接...")
        explorer.force_disconnect_sol()


def with_interrupt_handling():
    """带中断处理的使用示例"""
    print("\n=== 带中断处理的SOL使用示例 ===")
    
    explorer = BIOSMenuExplorer(
        host=SOL_CONFIG['host'],
        username=SOL_CONFIG['username'],
        password=SOL_CONFIG['password']
    )
    
    def cleanup_handler(signum, frame):
        print(f"\n收到信号 {signum}，正在清理SOL连接...")
        try:
            explorer.force_disconnect_sol()
        except Exception as e:
            print(f"清理时出错: {e}")
        print("清理完成，退出程序")
        sys.exit(0)
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, cleanup_handler)
    signal.signal(signal.SIGTERM, cleanup_handler)
    
    try:
        print("1. 启动SOL连接...")
        if not explorer.start_sol_session():
            print("❌ SOL连接失败")
            return
        
        print("✅ SOL连接成功")
        print("2. 执行长时间操作（可以按Ctrl+C测试中断处理）...")
        
        # 模拟长时间操作
        for i in range(30):
            print(f"   操作进度: {i+1}/30")
            time.sleep(1)
            
            # 每5秒读取一次内容
            if (i + 1) % 5 == 0:
                content = explorer.read_screen_content()
                print(f"   读取内容长度: {len(content)}")
        
        print("3. 操作完成，正常关闭SOL连接...")
        explorer.close_sol_session()
        print("✅ 操作完成")
        
    except KeyboardInterrupt:
        print("\n用户中断操作")
        explorer.force_disconnect_sol()
    except Exception as e:
        print(f"❌ 操作失败: {e}")
        explorer.force_disconnect_sol()


def multiple_operations_example():
    """多次操作示例"""
    print("\n=== 多次SOL操作示例 ===")
    
    operations = [
        "读取主页面内容",
        "切换到Advanced TAB",
        "读取Advanced页面内容",
        "回到主页面"
    ]
    
    for i, operation in enumerate(operations, 1):
        print(f"\n--- 操作 {i}: {operation} ---")
        
        explorer = BIOSMenuExplorer(
            host=SOL_CONFIG['host'],
            username=SOL_CONFIG['username'],
            password=SOL_CONFIG['password']
        )
        
        try:
            # 连接
            if not explorer.start_sol_session():
                print(f"❌ 操作 {i} 连接失败")
                continue
            
            # 执行具体操作
            if operation == "读取主页面内容":
                content = explorer.go_to_main_page()
                tab_name = explorer._extract_tab_name(content, 0)
                print(f"   主页面TAB: {tab_name}")
                
            elif operation == "切换到Advanced TAB":
                explorer.go_to_main_page()
                from bios_menu_explorer import KeyCode
                explorer.send_key(KeyCode.RIGHT, delay=1.0)
                content = explorer.read_screen_content()
                tab_name = explorer._extract_tab_name(content, 1)
                print(f"   切换后TAB: {tab_name}")
                
            elif operation == "读取Advanced页面内容":
                explorer.go_to_main_page()
                explorer.send_key(KeyCode.RIGHT, delay=1.0)
                content = explorer.read_screen_content()
                print(f"   Advanced页面内容长度: {len(content)}")
                
            elif operation == "回到主页面":
                content = explorer.go_to_main_page()
                print(f"   主页面内容长度: {len(content)}")
            
            print(f"✅ 操作 {i} 成功")
            
        except Exception as e:
            print(f"❌ 操作 {i} 失败: {e}")
        finally:
            # 确保每次操作后都正确断开
            try:
                explorer.close_sol_session()
            except Exception as e:
                print(f"关闭连接时出错: {e}")
                explorer.force_disconnect_sol()
        
        # 操作间隔
        time.sleep(1)


def full_exploration_with_proper_cleanup():
    """完整遍历示例（带正确的清理）"""
    print("\n=== 完整遍历示例（带正确清理） ===")
    
    explorer = BIOSMenuExplorer(
        host=SOL_CONFIG['host'],
        username=SOL_CONFIG['username'],
        password=SOL_CONFIG['password']
    )
    
    try:
        print("开始完整的BIOS菜单遍历...")
        
        # 使用run_full_exploration，它内部已经包含了正确的清理逻辑
        menu_structure = explorer.run_full_exploration()
        
        if menu_structure:
            print("✅ 遍历成功完成")
            print(f"发现 {len(menu_structure)} 个TAB页")
            for tab_name in menu_structure.keys():
                print(f"   - {tab_name}")
        else:
            print("❌ 遍历失败或未发现菜单结构")
            
    except Exception as e:
        print(f"❌ 遍历过程出错: {e}")


def manual_disconnect_methods():
    """手动断开方法示例"""
    print("\n=== 手动断开方法示例 ===")
    
    explorer = BIOSMenuExplorer(
        host=SOL_CONFIG['host'],
        username=SOL_CONFIG['username'],
        password=SOL_CONFIG['password']
    )
    
    try:
        print("1. 启动SOL连接...")
        if not explorer.start_sol_session():
            print("❌ SOL连接失败")
            return
        
        print("✅ SOL连接成功")
        time.sleep(2)
        
        print("2. 测试不同的断开方法...")
        
        # 方法1: 发送SOL deactivate命令
        print("   方法1: 使用deactivate命令")
        explorer.deactivate_sol()
        time.sleep(1)
        
        # 检查进程状态
        if explorer.sol_process and explorer.sol_process.poll() is None:
            print("   进程仍在运行，尝试方法2")
            
            # 方法2: 发送断开序列
            print("   方法2: 发送断开序列")
            explorer._send_sol_deactivate()
            time.sleep(1)
            
            if explorer.sol_process and explorer.sol_process.poll() is None:
                print("   进程仍在运行，使用方法3")
                
                # 方法3: 强制终止
                print("   方法3: 强制终止进程")
                explorer.sol_process.terminate()
                try:
                    explorer.sol_process.wait(timeout=3)
                    print("   进程已终止")
                except:
                    print("   强制杀死进程")
                    explorer.sol_process.kill()
        else:
            print("   deactivate命令成功")
        
        explorer.sol_process = None
        print("✅ 手动断开完成")
        
    except Exception as e:
        print(f"❌ 手动断开失败: {e}")


def main():
    """主函数"""
    print("SOL断开功能使用示例")
    print("=" * 50)
    
    examples = [
        ("基本使用", basic_usage_example),
        ("多次操作", multiple_operations_example),
        ("完整遍历", full_exploration_with_proper_cleanup),
        ("手动断开方法", manual_disconnect_methods),
        ("中断处理", with_interrupt_handling),
    ]
    
    print("可用的示例:")
    for i, (name, _) in enumerate(examples, 1):
        print(f"{i}. {name}")
    print("6. 运行所有示例")
    
    choice = input("\n请选择要运行的示例 (1-6): ").strip()
    
    if choice == "6":
        # 运行所有示例（除了中断处理）
        for name, func in examples[:-1]:  # 排除中断处理
            print(f"\n{'='*20} {name} {'='*20}")
            try:
                func()
            except Exception as e:
                print(f"示例 {name} 执行失败: {e}")
            time.sleep(2)
    elif choice.isdigit() and 1 <= int(choice) <= len(examples):
        name, func = examples[int(choice) - 1]
        print(f"\n{'='*20} {name} {'='*20}")
        func()
    else:
        print("无效选择")
        return
    
    print(f"\n{'='*20} 示例完成 {'='*20}")
    print("主要功能:")
    print("✅ 正常SOL断开 - close_sol_session()")
    print("✅ 强制SOL断开 - force_disconnect_sol()")
    print("✅ SOL deactivate命令 - deactivate_sol()")
    print("✅ 中断信号处理")
    print("✅ 异常情况下的自动清理")


if __name__ == "__main__":
    main()
