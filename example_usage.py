#!/usr/bin/env python3
"""
BIOS菜单遍历器使用示例
演示如何使用BIOSMenuExplorer来获取BIOS菜单结构
"""

from bios_menu_explorer import BIOSMenuExplorer, KeyCode
import json
import time


def simple_exploration_example():
    """简单的遍历示例 - 包含优化的退出提示处理"""
    print("=== 简单BIOS菜单遍历示例 (优化版) ===")

    # 使用与simple_git_bios_info.py相同的连接参数
    HOST = "************"
    USERNAME = "Administrator"
    PASSWORD = "Superuser9!"

    # 创建遍历器
    explorer = BIOSMenuExplorer(HOST, USERNAME, PASSWORD)

    try:
        # 启动SOL会话
        if not explorer.start_sol_session():
            print("无法建立SOL连接")
            return

        print("SOL连接成功，开始遍历...")

        # 使用优化的回到主页面功能（智能处理退出提示）
        print("确保在主页面...")
        explorer.go_to_main_page()

        # 验证是否在主页面
        current_content = explorer.read_screen_content()
        if explorer._is_main_page(current_content):
            print("✅ 确认在主页面")
        else:
            print("⚠️  可能不在主页面")

        # 检测TAB数量（已注释掉自动检测，使用固定值）
        # tab_count = explorer.detect_tab_count()
        # print(f"检测到 {tab_count} 个TAB页")

        # 遍历所有TAB（当前设置为只遍历第一个TAB）
        menu_structure = explorer.explore_all_tabs()

        # 显示结果摘要
        print("\n=== 遍历结果摘要 ===")
        for tab_name, tab_data in menu_structure.items():
            print(f"TAB: {tab_name}")
            print(f"  路径: {tab_data['path']}")
            print(f"  子菜单数: {len(tab_data['children'])}")

            # 显示子菜单
            for child in tab_data['children'][:3]:  # 只显示前3个
                print(f"    - {child['name']}")

            if len(tab_data['children']) > 3:
                print(f"    ... 还有 {len(tab_data['children']) - 3} 个子菜单")
            print()

        # 保存详细结果
        explorer.save_menu_structure(menu_structure, "bios_structure_detailed.json")

        print("✅ 遍历完成，支持智能退出提示处理")

    except Exception as e:
        print(f"遍历过程出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        explorer.close_sol_session()


def manual_navigation_example():
    """手动导航示例"""
    print("=== 手动导航示例 ===")
    
    HOST = "************"
    USERNAME = "Administrator"
    PASSWORD = "Superuser9!"
    
    explorer = BIOSMenuExplorer(HOST, USERNAME, PASSWORD)
    
    try:
        if not explorer.start_sol_session():
            print("无法建立SOL连接")
            return
        
        print("手动导航演示...")
        
        # 读取初始屏幕
        initial_content = explorer.read_screen_content()
        print("初始屏幕内容长度:", len(initial_content))
        
        # 向右切换TAB
        print("向右切换TAB...")
        explorer.send_key(KeyCode.RIGHT)
        right_content = explorer.read_screen_content()
        print("右TAB内容长度:", len(right_content))
        
        # 向下导航
        print("向下导航...")
        for i in range(3):
            explorer.send_key(KeyCode.DOWN, 0.3)
            print(f"  下移 {i+1} 步")
        
        # 尝试进入子菜单
        print("尝试进入子菜单...")
        before_enter = explorer.read_screen_content()
        explorer.send_key(KeyCode.ENTER)
        after_enter = explorer.read_screen_content()
        
        if len(after_enter) != len(before_enter):
            print("成功进入子菜单")
            
            # 返回上级
            print("返回上级菜单...")
            explorer.send_key(KeyCode.ESC)
        else:
            print("未能进入子菜单")
        
        # 回到主页
        print("回到主页...")
        explorer.go_to_main_page()
        
    except Exception as e:
        print(f"手动导航出错: {e}")
    finally:
        explorer.close_sol_session()


def analyze_saved_structure():
    """分析已保存的菜单结构"""
    print("=== 分析已保存的菜单结构 ===")
    
    try:
        with open("bios_menu_structure.json", 'r', encoding='utf-8') as f:
            structure = json.load(f)
        
        print("BIOS菜单结构分析:")
        print(f"总TAB数: {len(structure)}")
        
        total_menus = 0
        max_depth = 0
        
        for tab_name, tab_data in structure.items():
            print(f"\nTAB: {tab_name}")
            print(f"  级别: {tab_data['level']}")
            print(f"  是否为子菜单: {tab_data['is_submenu']}")
            
            # 递归计算菜单数量和深度
            menu_count, depth = count_menus_and_depth(tab_data)
            total_menus += menu_count
            max_depth = max(max_depth, depth)
            
            print(f"  子菜单总数: {menu_count}")
            print(f"  最大深度: {depth}")
        
        print(f"\n总体统计:")
        print(f"  总菜单项数: {total_menus}")
        print(f"  最大菜单深度: {max_depth}")
        
    except FileNotFoundError:
        print("未找到保存的菜单结构文件，请先运行遍历")
    except Exception as e:
        print(f"分析菜单结构时出错: {e}")


def count_menus_and_depth(node, current_depth=0):
    """递归计算菜单数量和深度"""
    count = 1  # 当前节点
    max_depth = current_depth
    
    for child in node.get('children', []):
        child_count, child_depth = count_menus_and_depth(child, current_depth + 1)
        count += child_count
        max_depth = max(max_depth, child_depth)
    
    return count, max_depth


def create_menu_summary():
    """创建菜单结构摘要"""
    print("=== 创建菜单结构摘要 ===")
    
    try:
        with open("bios_menu_structure.json", 'r', encoding='utf-8') as f:
            structure = json.load(f)
        
        summary = {
            "bios_info": {
                "total_tabs": len(structure),
                "exploration_time": time.strftime("%Y-%m-%d %H:%M:%S"),
                "tabs": []
            }
        }
        
        for tab_name, tab_data in structure.items():
            tab_summary = {
                "name": tab_name,
                "path": tab_data['path'],
                "menu_count": len(tab_data['children']),
                "menus": []
            }
            
            for child in tab_data['children']:
                menu_info = {
                    "name": child['name'],
                    "path": child['path'],
                    "has_submenus": len(child['children']) > 0,
                    "submenu_count": len(child['children'])
                }
                tab_summary["menus"].append(menu_info)
            
            summary["bios_info"]["tabs"].append(tab_summary)
        
        # 保存摘要
        with open("bios_menu_summary.json", 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        
        print("菜单摘要已保存到: bios_menu_summary.json")
        
        # 显示摘要
        print(f"\nBIOS菜单摘要:")
        print(f"  总TAB数: {summary['bios_info']['total_tabs']}")
        print(f"  遍历时间: {summary['bios_info']['exploration_time']}")
        
        for tab in summary['bios_info']['tabs']:
            print(f"\n  TAB: {tab['name']}")
            print(f"    菜单项数: {tab['menu_count']}")
            for menu in tab['menus'][:3]:  # 显示前3个
                print(f"      - {menu['name']} (子菜单: {menu['submenu_count']})")
            if len(tab['menus']) > 3:
                print(f"      ... 还有 {len(tab['menus']) - 3} 个菜单项")
        
    except Exception as e:
        print(f"创建摘要时出错: {e}")


if __name__ == "__main__":
    print("BIOS菜单遍历器使用示例")
    print("请选择要运行的示例:")
    print("1. 完整遍历 (推荐)")
    print("2. 手动导航演示")
    print("3. 分析已保存的结构")
    print("4. 创建菜单摘要")
    
    choice = input("请输入选择 (1-4): ").strip()
    
    if choice == "1":
        simple_exploration_example()
    elif choice == "2":
        manual_navigation_example()
    elif choice == "3":
        analyze_saved_structure()
    elif choice == "4":
        create_menu_summary()
    else:
        print("无效选择，运行完整遍历...")
        simple_exploration_example()
