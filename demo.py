#!/usr/bin/env python3
"""
BIOS菜单遍历器演示脚本
快速演示如何使用BIOS菜单遍历功能
"""

from bios_menu_explorer import BIOSMenuExplorer, KeyCode
from config import SOL_CONFIG, get_config_summary
import json
import time


def demo_basic_usage():
    """基本使用演示"""
    print("=== BIOS菜单遍历器基本使用演示 ===\n")
    
    # 显示配置信息
    print("1. 当前配置:")
    config_summary = get_config_summary()
    for key, value in config_summary.items():
        print(f"   {key}: {value}")
    
    print(f"\n2. 连接信息:")
    print(f"   主机: {SOL_CONFIG['host']}")
    print(f"   用户: {SOL_CONFIG['username']}")
    print(f"   接口: {SOL_CONFIG['interface']}")
    
    # 创建遍历器实例
    print(f"\n3. 创建遍历器实例...")
    explorer = BIOSMenuExplorer(
        host=SOL_CONFIG['host'],
        username=SOL_CONFIG['username'],
        password=SOL_CONFIG['password']
    )
    
    print("   遍历器创建成功")
    print(f"   支持的按键: {[key.name for key in KeyCode]}")
    
    return explorer


def demo_manual_navigation():
    """手动导航演示"""
    print("\n=== 手动导航演示 ===\n")
    
    explorer = demo_basic_usage()
    
    print("4. 手动导航演示 (模拟模式):")
    print("   注意: 这是演示模式，不会实际连接到BIOS")
    
    # 模拟按键操作
    navigation_steps = [
        (KeyCode.RIGHT, "切换到下一个TAB"),
        (KeyCode.DOWN, "向下移动到下一个菜单项"),
        (KeyCode.DOWN, "继续向下移动"),
        (KeyCode.ENTER, "尝试进入子菜单"),
        (KeyCode.ESC, "返回上级菜单"),
        (KeyCode.LEFT, "切换到上一个TAB")
    ]
    
    for i, (key, description) in enumerate(navigation_steps, 1):
        print(f"   步骤 {i}: {description} ({key.name})")
        print(f"          按键代码: {repr(key.value)}")
        time.sleep(0.5)  # 演示延迟
    
    print("\n   手动导航演示完成")


def demo_structure_analysis():
    """结构分析演示"""
    print("\n=== 菜单结构分析演示 ===\n")
    
    # 创建示例菜单结构
    sample_structure = {
        "Main": {
            "name": "Main",
            "path": "/Main",
            "level": 0,
            "tab_index": 0,
            "parent_path": "",
            "children": [
                {
                    "name": "System Information",
                    "path": "/Main/System Information",
                    "level": 1,
                    "tab_index": 0,
                    "parent_path": "/Main",
                    "children": [],
                    "content": "System Information Content",
                    "is_submenu": True,
                    "is_configurable": False
                },
                {
                    "name": "Boot Configuration",
                    "path": "/Main/Boot Configuration", 
                    "level": 1,
                    "tab_index": 0,
                    "parent_path": "/Main",
                    "children": [
                        {
                            "name": "Boot Order",
                            "path": "/Main/Boot Configuration/Boot Order",
                            "level": 2,
                            "tab_index": 0,
                            "parent_path": "/Main/Boot Configuration",
                            "children": [],
                            "content": "Boot Order Settings",
                            "is_submenu": True,
                            "is_configurable": True
                        }
                    ],
                    "content": "Boot Configuration Content",
                    "is_submenu": True,
                    "is_configurable": False
                }
            ],
            "content": "Main Menu Content",
            "is_submenu": False,
            "is_configurable": False
        },
        "Advanced": {
            "name": "Advanced",
            "path": "/Advanced",
            "level": 0,
            "tab_index": 1,
            "parent_path": "",
            "children": [
                {
                    "name": "CPU Configuration",
                    "path": "/Advanced/CPU Configuration",
                    "level": 1,
                    "tab_index": 1,
                    "parent_path": "/Advanced",
                    "children": [],
                    "content": "CPU Configuration Content",
                    "is_submenu": True,
                    "is_configurable": True
                }
            ],
            "content": "Advanced Menu Content",
            "is_submenu": False,
            "is_configurable": False
        }
    }
    
    print("5. 示例菜单结构分析:")
    print(f"   总TAB数: {len(sample_structure)}")
    
    total_menus = 0
    max_depth = 0
    configurable_items = 0
    
    for tab_name, tab_data in sample_structure.items():
        print(f"\n   TAB: {tab_name}")
        print(f"     路径: {tab_data['path']}")
        print(f"     级别: {tab_data['level']}")
        print(f"     子菜单数: {len(tab_data['children'])}")
        
        # 递归计算统计信息
        menu_count, depth, config_count = analyze_menu_recursive(tab_data)
        total_menus += menu_count
        max_depth = max(max_depth, depth)
        configurable_items += config_count
        
        print(f"     总菜单项: {menu_count}")
        print(f"     最大深度: {depth}")
        print(f"     可配置项: {config_count}")
    
    print(f"\n   整体统计:")
    print(f"     总菜单项数: {total_menus}")
    print(f"     最大深度: {max_depth}")
    print(f"     可配置项数: {configurable_items}")
    
    # 保存示例结构
    with open("demo_structure.json", 'w', encoding='utf-8') as f:
        json.dump(sample_structure, f, ensure_ascii=False, indent=2)
    print(f"\n   示例结构已保存到: demo_structure.json")


def analyze_menu_recursive(node, current_depth=0):
    """递归分析菜单节点"""
    count = 1
    max_depth = current_depth
    config_count = 1 if node.get('is_configurable', False) else 0
    
    for child in node.get('children', []):
        child_count, child_depth, child_config = analyze_menu_recursive(child, current_depth + 1)
        count += child_count
        max_depth = max(max_depth, child_depth)
        config_count += child_config
    
    return count, max_depth, config_count


def demo_key_codes():
    """按键代码演示"""
    print("\n=== 支持的按键代码演示 ===\n")
    
    print("6. BIOS导航按键代码:")
    key_descriptions = {
        KeyCode.UP: "向上箭头 - 向上移动菜单项",
        KeyCode.DOWN: "向下箭头 - 向下移动菜单项", 
        KeyCode.LEFT: "向左箭头 - 切换到上一个TAB",
        KeyCode.RIGHT: "向右箭头 - 切换到下一个TAB",
        KeyCode.ENTER: "回车键 - 进入子菜单或确认选择",
        KeyCode.ESC: "ESC键 - 返回上级菜单",
        KeyCode.TAB: "TAB键 - 在字段间切换",
        KeyCode.F1: "F1键 - 通常显示帮助",
        KeyCode.F10: "F10键 - 通常保存并退出"
    }
    
    for key, description in key_descriptions.items():
        print(f"   {key.name:>6}: {repr(key.value):>8} - {description}")


def demo_usage_scenarios():
    """使用场景演示"""
    print("\n=== 使用场景演示 ===\n")
    
    scenarios = [
        {
            "name": "完整BIOS结构获取",
            "description": "自动遍历所有TAB页和子菜单，生成完整的菜单结构",
            "code": """
explorer = BIOSMenuExplorer(host, username, password)
structure = explorer.run_full_exploration()
explorer.save_menu_structure(structure, "full_bios_structure.json")
            """.strip()
        },
        {
            "name": "特定TAB遍历",
            "description": "只遍历特定的TAB页面，如Advanced设置",
            "code": """
explorer.start_sol_session()
explorer.go_to_main_page()
explorer.send_key(KeyCode.RIGHT)  # 切换到Advanced TAB
tab_structure = explorer.explore_current_tab("Advanced", 1)
            """.strip()
        },
        {
            "name": "手动导航控制",
            "description": "精确控制导航过程，适用于特定配置修改",
            "code": """
explorer.start_sol_session()
explorer.send_key(KeyCode.DOWN)    # 向下移动
explorer.send_key(KeyCode.ENTER)   # 进入子菜单
content = explorer.read_screen_content()  # 读取当前内容
explorer.send_key(KeyCode.ESC)     # 返回上级
            """.strip()
        }
    ]
    
    print("7. 常见使用场景:")
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n   场景 {i}: {scenario['name']}")
        print(f"   描述: {scenario['description']}")
        print(f"   代码示例:")
        for line in scenario['code'].split('\n'):
            print(f"     {line}")


def main():
    """主演示函数"""
    print("BIOS菜单遍历器 - 功能演示")
    print("=" * 50)
    
    try:
        # 运行各个演示
        demo_basic_usage()
        demo_manual_navigation()
        demo_structure_analysis()
        demo_key_codes()
        demo_usage_scenarios()
        
        print("\n" + "=" * 50)
        print("演示完成！")
        print("\n下一步操作建议:")
        print("1. 运行 'python example_usage.py' 查看实际使用示例")
        print("2. 运行 'python test_bios_explorer.py' 执行单元测试")
        print("3. 修改 config.py 中的连接参数")
        print("4. 使用 BIOSMenuExplorer 类开始实际的BIOS遍历")
        
    except Exception as e:
        print(f"\n演示过程中出错: {e}")
        print("请检查代码和配置是否正确")


if __name__ == "__main__":
    main()
