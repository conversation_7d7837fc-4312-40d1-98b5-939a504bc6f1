#!/usr/bin/env python3
"""
测试ANSI转义字符在decode后的正则匹配
验证byte decode后ANSI转义字符是否能正常匹配
"""

import re


def test_ansi_decode_matching():
    """测试ANSI转义字符decode后的匹配"""
    print("=== 测试ANSI转义字符decode后的匹配 ===")
    
    # 模拟从SOL读取的原始字节数据
    raw_bios_data = b'\x1b[2J\x1b[H\x1b[0;37;40mBIOS Setup Utility\n\x1b[1;37;44m Main \x1b[0;37;40m Advanced \x1b[0;37;40m Security\n\x1b[3;1H\x1b[0;37;40mSystem Information\n'
    
    print("1. 原始字节数据:")
    print(f"   类型: {type(raw_bios_data)}")
    print(f"   长度: {len(raw_bios_data)} 字节")
    print(f"   内容: {raw_bios_data[:50]}...")
    
    # decode操作
    decoded_string = raw_bios_data.decode('utf-8', errors='ignore')
    
    print(f"\n2. decode后的字符串:")
    print(f"   类型: {type(decoded_string)}")
    print(f"   长度: {len(decoded_string)} 字符")
    print(f"   内容: {repr(decoded_string[:50])}")
    
    # 测试ANSI转义字符的正则匹配
    ansi_pattern = re.compile(r'\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])')
    
    print(f"\n3. ANSI转义字符匹配测试:")
    
    # 在原始字节中查找（需要先decode）
    matches_from_bytes = ansi_pattern.findall(decoded_string)
    print(f"   从decode后字符串中找到的ANSI序列: {len(matches_from_bytes)}")
    
    # 显示找到的ANSI序列
    for i, match in enumerate(matches_from_bytes[:5]):  # 只显示前5个
        print(f"     {i+1}. {repr(match)}")
    
    # 测试清理ANSI序列
    clean_content = ansi_pattern.sub('', decoded_string)
    print(f"\n4. 清理ANSI序列后:")
    print(f"   原始长度: {len(decoded_string)}")
    print(f"   清理后长度: {len(clean_content)}")
    print(f"   清理后内容: {repr(clean_content[:100])}")
    
    return {
        "original_length": len(decoded_string),
        "ansi_sequences_found": len(matches_from_bytes),
        "clean_length": len(clean_content),
        "ansi_sequences": matches_from_bytes[:5]
    }


def test_specific_ansi_sequences():
    """测试特定的ANSI转义序列"""
    print("\n=== 测试特定ANSI转义序列 ===")
    
    # 常见的BIOS ANSI序列
    test_sequences = [
        (b'\x1b[A', "上箭头"),
        (b'\x1b[B', "下箭头"), 
        (b'\x1b[C', "右箭头"),
        (b'\x1b[D', "左箭头"),
        (b'\x1b[2J', "清屏"),
        (b'\x1b[H', "光标回到原点"),
        (b'\x1b[0;37;40m', "颜色设置"),
        (b'\x1b[1;37;44m', "高亮颜色"),
        (b'\x1b[3;1H', "光标定位")
    ]
    
    ansi_pattern = re.compile(r'\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])')
    
    print("测试各种ANSI序列的匹配:")
    for byte_seq, description in test_sequences:
        # decode字节序列
        decoded_seq = byte_seq.decode('utf-8', errors='ignore')
        
        # 测试正则匹配
        match = ansi_pattern.search(decoded_seq)
        
        status = "✅" if match else "❌"
        print(f"   {status} {description}: {repr(byte_seq)} -> {repr(decoded_seq)} -> {'匹配' if match else '不匹配'}")


def test_mixed_content():
    """测试混合内容（ANSI + 普通文本）"""
    print("\n=== 测试混合内容处理 ===")
    
    # 模拟真实的BIOS输出
    mixed_content_bytes = b'\x1b[2J\x1b[H\x1b[0;37;40mBIOS Setup Utility\n\x1b[1;37;44m Main \x1b[0;37;40m Advanced \x1b[0;37;40m Platform Configuration \x1b[0;37;40m Socket Configuration \x1b[0;37;40m Server Mgmt \x1b[0;37;40m Security \x1b[0;37;40m Boot \x1b[0;37;40m Save & Exit\n\x1b[3;1H\x1b[0;37;40mSystem Information\n\x1b[4;1H\x1b[0;37;40mProcessor Information\n'
    
    # decode
    decoded_content = mixed_content_bytes.decode('utf-8', errors='ignore')
    
    print("1. 原始混合内容:")
    print(f"   字节长度: {len(mixed_content_bytes)}")
    print(f"   字符长度: {len(decoded_content)}")
    
    # 查找ANSI序列
    ansi_pattern = re.compile(r'\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])')
    ansi_matches = ansi_pattern.findall(decoded_content)
    
    print(f"\n2. ANSI序列分析:")
    print(f"   找到的ANSI序列数量: {len(ansi_matches)}")
    print("   ANSI序列列表:")
    for i, seq in enumerate(ansi_matches):
        print(f"     {i+1}. {repr(seq)}")
    
    # 清理ANSI序列
    clean_content = ansi_pattern.sub('', decoded_content)
    
    print(f"\n3. 清理后的内容:")
    print(f"   清理后长度: {len(clean_content)}")
    print("   清理后内容:")
    print(clean_content)
    
    # 验证TAB关键词是否能正确识别
    tab_keywords = ['main', 'advanced', 'platform configuration', 'socket configuration', 
                   'server mgmt', 'security', 'boot', 'save & exit']
    
    clean_lower = clean_content.lower()
    found_keywords = [kw for kw in tab_keywords if kw in clean_lower]
    
    print(f"\n4. TAB关键词识别:")
    print(f"   找到的关键词: {found_keywords}")
    print(f"   关键词数量: {len(found_keywords)}")
    
    return {
        "total_ansi": len(ansi_matches),
        "clean_content": clean_content,
        "found_keywords": found_keywords
    }


def test_bios_explorer_ansi_handling():
    """测试BIOSMenuExplorer的ANSI处理"""
    print("\n=== 测试BIOSMenuExplorer的ANSI处理 ===")
    
    from bios_menu_explorer import BIOSMenuExplorer
    
    # 创建测试实例
    explorer = BIOSMenuExplorer("test", "test", "test")
    
    # 模拟BIOS内容
    test_content = '\x1b[2J\x1b[H\x1b[0;37;40mBIOS Setup Utility\n\x1b[1;37;44m Main \x1b[0;37;40m Advanced \x1b[0;37;40m Platform Configuration \x1b[0;37;40m Socket Configuration \x1b[0;37;40m Server Mgmt \x1b[0;37;40m Security \x1b[0;37;40m Boot \x1b[0;37;40m Save & Exit\n\x1b[3;1H\x1b[0;37;40mSystem Information\n'
    
    print("1. 测试TAB名称提取:")
    tab_name = explorer._extract_tab_name(test_content, 0)
    print(f"   识别的TAB名称: {tab_name}")
    
    print("\n2. 测试退出提示检测:")
    exit_content = '\x1b[1;37;41mExit Without Saving\x1b[0m\nAre you sure?'
    is_exit_prompt = explorer._check_exit_prompt(exit_content)
    print(f"   退出提示检测: {is_exit_prompt}")
    
    print("\n3. 测试主页面检测:")
    is_main_page = explorer._is_main_page(test_content)
    print(f"   主页面检测: {is_main_page}")
    
    return {
        "tab_name": tab_name,
        "exit_prompt_detected": is_exit_prompt,
        "main_page_detected": is_main_page
    }


def main():
    """主测试函数"""
    print("ANSI转义字符decode后正则匹配测试")
    print("=" * 50)
    
    # 运行所有测试
    results = {}
    
    results["basic_decode"] = test_ansi_decode_matching()
    test_specific_ansi_sequences()
    results["mixed_content"] = test_mixed_content()
    results["bios_explorer"] = test_bios_explorer_ansi_handling()
    
    # 总结
    print(f"\n{'='*20} 测试总结 {'='*20}")
    print("✅ ANSI转义字符在decode后能正常匹配")
    print("✅ 正则表达式能正确识别各种ANSI序列")
    print("✅ 清理ANSI序列后能正确提取文本内容")
    print("✅ BIOSMenuExplorer的ANSI处理功能正常")
    
    print(f"\n关键发现:")
    print(f"- decode操作不影响ANSI转义字符的表示")
    print(f"- 正则模式 r'\\x1B(?:[@-Z\\\\-_]|\\[[0-?]*[ -/]*[@-~])' 能正确匹配")
    print(f"- TAB关键词识别在清理ANSI后工作正常")
    
    return results


if __name__ == "__main__":
    main()
