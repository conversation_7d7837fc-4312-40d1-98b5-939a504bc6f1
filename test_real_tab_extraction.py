#!/usr/bin/env python3
"""
测试真实BIOS TAB名称提取
基于实际的BIOS输出测试TAB名称识别功能
"""

from bios_menu_explorer import BIOSMenuExplorer
import re


def test_real_bios_output():
    """测试真实的BIOS输出"""
    print("=== 测试真实BIOS输出的TAB提取 ===")
    
    # 您提供的真实BIOS输出
    real_bios_output = r"""'\x1b[1;1H\x1b[1m\x1b[1;1H                               Aptio Setup - AMI                                \x1b[2;1H\x1b[0m   \x1b[0m Main \x1b[0m Advanced  Platform Configuration  Socket Configuration  Server Mgmt  Security  Boot  Save & Exit\x1b[3;1H\x1b[0m-----------------------------------------------+-------------------------\x1b[4;1H|                                               |                         |\x1b[5;1H|  \x1b[1mBIOS Version\x1b[0m             \x1b[1m04.25.02.2...1b[0m             *|                         |\x1b[6;1H|  \x1b[1mBuild Date\x1b[0m               \x1b[1m06/30/2025\x1b[0m              *|                         |\x1b[7;1H|                                               *|                         |\x1b[8;1H|  \x1b[1mDebug Mode Status\x1b[0m        \x1b[1mDisabled\x1b[0m                *|                         |\x1b[9;1H|                                                   *|                         |\x1b[10;1H|  \x1b[1mProduct Name\x1b[0m             \x1b[1mR5300 G5\x1b[0m                *|                         |\x1b[11;1H|  \x1b[1mSerial Number\x1b[0m            \x1b[1mRcdTAu1nTm6tOO\x1b[0m          *|                         |\x1b[12;1H|                           \x1b[1mbptXKey6eBMJrUjdaZt2Yen\x1b[0m *|><: Select Screen        |\x1b[13;1H|                           \x1b[1mguox1BrXeKKbZYpLl\x1b[0m       *|^v: Select Item          |\x1b[14;1H|  \x1b[1mAsset Tag\x1b[0m                \x1b[1mMI05102A\x1b[0m                *|Enter: Select            |\x1b[15;1H|  \x1b[1mAccess Level\x1b[0m             \x1b[1mAdministrator\x1b[0m           +|+/-: Change Option       |\x1b[16;1H|                                               +|F1: General Help         |\x1b[17;1H|  \x1b[0mPlatform Information                           \x1b[0m+|k/m: Scroll Help Area    |\x1b[18;1H|  \x1b[1mPlatform\x1b[0m                 \x1b[1mTypeArcherCityRP\x1b[0m        +|F2: Previous Values      |\x1b[19;1H|  \x1b[1mCPU\x1b[0m                      \x1b[1mC06F2 - EMR-SP Rx\x1b[0m       +|F3: Optimized Defaults   |\x1b[20;1H|  \x1b[1mPCH\x1b[0m                      \x1b[1mC741 SKU - B1\x1b[0m           +|F4: Save & Exit          |\x1b[21;1H|                           \x1b[1mB1\x1b[0m                      v|F7: Search Setup Items   |\x1b[22;1H|                                                    |ESC: Exit                |\x1b[23;1H\----------------------------------------------------+-------------------------\x1b[24;1H\x1b[0m                    Version 2.22.1290 Copyright (C) 2025 AMI                   """
    
    # 移除外层的引号
    if real_bios_output.startswith("'") and real_bios_output.endswith("'"):
        real_bios_output = real_bios_output[1:-1]
    
    # 处理转义字符
    real_bios_output = real_bios_output.encode().decode('unicode_escape')
    
    print("1. 原始输出分析:")
    print(f"   内容长度: {len(real_bios_output)}")
    print(f"   内容类型: {type(real_bios_output)}")
    
    # 清理ANSI转义字符
    ansi_escape = re.compile(r'\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])')
    clean_content = ansi_escape.sub('', real_bios_output)
    
    print(f"\n2. 清理后内容:")
    print(f"   清理后长度: {len(clean_content)}")
    
    # 显示前几行内容
    lines = clean_content.split('\n')
    print(f"   总行数: {len(lines)}")
    print(f"   前10行内容:")
    for i, line in enumerate(lines[:10]):
        if line.strip():
            print(f"     行{i+1}: {repr(line)}")
    
    # 查找TAB行
    print(f"\n3. 查找TAB导航行:")
    tab_keywords = ['main', 'advanced', 'platform configuration', 'socket configuration', 
                   'server mgmt', 'security', 'boot', 'save & exit']
    
    for i, line in enumerate(lines[:10]):
        line_lower = line.lower()
        keyword_count = sum(1 for keyword in tab_keywords if keyword in line_lower)
        if keyword_count >= 2:
            print(f"   行{i+1} (包含{keyword_count}个关键词): {line}")
    
    # 测试TAB名称提取
    print(f"\n4. 测试TAB名称提取:")
    explorer = BIOSMenuExplorer("test", "test", "test")
    
    for tab_index in range(5):  # 测试前5个TAB
        tab_name = explorer._extract_tab_name(real_bios_output, tab_index)
        print(f"   TAB {tab_index}: {tab_name}")
    
    return clean_content


def test_tab_line_parsing():
    """测试TAB行解析功能"""
    print("\n=== 测试TAB行解析功能 ===")
    
    # 从真实输出中提取的TAB行
    tab_line = "   Main  Advanced  Platform Configuration  Socket Configuration  Server Mgmt  Security  Boot  Save & Exit"
    
    print(f"测试TAB行: {tab_line}")
    
    explorer = BIOSMenuExplorer("test", "test", "test")
    tab_names = explorer._parse_tab_names_from_line(tab_line)
    
    print(f"解析结果: {tab_names}")
    print(f"TAB数量: {len(tab_names)}")
    
    # 验证每个TAB名称
    expected_tabs = ['Main', 'Advanced', 'Platform Configuration', 'Socket Configuration', 
                    'Server Mgmt', 'Security', 'Boot', 'Save & Exit']
    
    print(f"\n期望的TAB: {expected_tabs}")
    print(f"实际解析: {tab_names}")
    print(f"匹配度: {len(set(tab_names) & set(expected_tabs))}/{len(expected_tabs)}")
    
    return tab_names


def test_improved_extraction():
    """测试改进后的提取功能"""
    print("\n=== 测试改进后的TAB提取功能 ===")
    
    # 构造测试内容（基于真实输出）
    test_content = """                               Aptio Setup - AMI                                
   Main  Advanced  Platform Configuration  Socket Configuration  Server Mgmt  Security  Boot  Save & Exit
-----------------------------------------------+-------------------------
|                                               |                         |
|  BIOS Version             04.25.02.2          |                         |
|  Build Date               06/30/2025          |                         |
|                                               |                         |
|  Debug Mode Status        Disabled            |                         |
"""
    
    print("测试内容:")
    print(test_content)
    
    explorer = BIOSMenuExplorer("test", "test", "test")
    
    print(f"\nTAB提取测试:")
    for i in range(8):
        tab_name = explorer._extract_tab_name(test_content, i)
        print(f"  TAB {i}: {tab_name}")
    
    return test_content


def test_edge_cases():
    """测试边界情况"""
    print("\n=== 测试边界情况 ===")
    
    explorer = BIOSMenuExplorer("test", "test", "test")
    
    test_cases = [
        {
            "name": "空内容",
            "content": "",
            "expected": "Tab_1"
        },
        {
            "name": "只有标题",
            "content": "BIOS Setup Utility",
            "expected": "Tab_1"
        },
        {
            "name": "部分TAB",
            "content": "Main  Advanced  Security",
            "expected": "Main"
        },
        {
            "name": "单个TAB",
            "content": "Main Menu\nSystem Information",
            "expected": "Main"
        }
    ]
    
    for test_case in test_cases:
        print(f"\n测试: {test_case['name']}")
        result = explorer._extract_tab_name(test_case['content'], 0)
        status = "✅" if test_case['expected'].lower() in result.lower() else "❌"
        print(f"  结果: {result}")
        print(f"  期望: {test_case['expected']}")
        print(f"  状态: {status}")


def main():
    """主测试函数"""
    print("真实BIOS TAB名称提取测试")
    print("=" * 50)
    
    # 运行所有测试
    test_real_bios_output()
    test_tab_line_parsing()
    test_improved_extraction()
    test_edge_cases()
    
    print(f"\n{'='*20} 测试总结 {'='*20}")
    print("主要改进:")
    print("✅ 降低TAB行检测阈值（从2个关键词降到3个）")
    print("✅ 增加详细的调试输出")
    print("✅ 改进TAB名称解析逻辑")
    print("✅ 按顺序匹配预定义的TAB模式")
    print("✅ 处理真实BIOS输出格式")
    
    print(f"\n建议:")
    print("- 如果仍然识别为Tab_1，请检查调试输出")
    print("- 确认TAB导航行是否被正确识别")
    print("- 验证关键词匹配是否正常工作")


if __name__ == "__main__":
    main()
