'\x1b[1;1H\x1b[1m\x1b[1;1H                               Aptio Setup - AMI                                \x1b[2;1H\x1b[0m    Main  Advanced  Platform Configuration  Socket Configuration \x1b[0m Server Mgmt \x1b[0m >\x1b[3;1H\x1b[0m/----------------------------------------------------+-------------------------\\\x1b[4;1H|                                                   ^|Enabled/Disabled Set     |\x1b[5;1H|  POST Timer               [Enabled]               +|BMC to default.          |\x1b[6;1H|  POST Timer timeout       15                      +|                         |\x1b[7;1H|  POST Timer Policy        [Power Cycle]           +|                         |\x1b[8;1H|  OS Watchdog Timer        [Disabled]              +|                         |\x1b[9;1H|  \x1b[1mOS Wtd Timer Timeout\x1b[0m     \x1b[1m20  \x1b[0m                    *|                         |\x1b[10;1H|  \x1b[1mOS Wtd Timer Policy\x1b[0m      \x1b[1m[Power Cycle]      \x1b[0m     *|                         |\x1b[11;1H|  SOL                      [Enabled]               *|-------------------------|\x1b[12;1H|                                                   *|><: Select Screen        |\x1b[13;1H|                                                   *|^v: Select Item          |\x1b[14;1H|  Restore on AC Power      [Last State]            *|Enter: Select            |\x1b[15;1H|  Loss                                             *|+/-: Change Option       |\x1b[16;1H|  \x1b[1mPower Control Policy\x1b[0m     \x1b[1mLast State\x1b[0m              *|k/m: Scroll Help Area    |\x1b[17;1H|  \x1b[1mStatus\x1b[0m                                           *|F1: General Help         |\x1b[18;1H|                                                   *|F2: Previous Values      |\x1b[19;1H|  \x1b[1mSet BMC to default\x1b[0m       \x1b[1m[Disabled]\x1b[0m              +|F3: Optimized Defaults   |\x1b[20;1H|                                                   +|F4: Save & Exit          |\x1b[21;1H|                                                   v|F7: Search Setup Items   |\x1b[22;1H|                                                    |ESC: Exit                |\x1b[23;1H\\----------------------------------------------------+-------------------------/\x1b[24;1H\x1b[0m                    Version 2.22.1290 Copyright (C) 2025 AMI                   '