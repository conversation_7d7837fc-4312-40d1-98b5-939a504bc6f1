#!/usr/bin/env python3
"""
优化后的BIOS菜单遍历使用示例
展示如何使用改进的内容缓存和退出提示处理功能
"""

from bios_menu_explorer import BIOSMenuExplorer, KeyCode
from config import SOL_CONFIG
import time


def optimized_single_tab_exploration():
    """优化的单TAB遍历示例"""
    print("=== 优化的单TAB遍历示例 ===")
    
    explorer = BIOSMenuExplorer(
        host=SOL_CONFIG['host'],
        username=SOL_CONFIG['username'],
        password=SOL_CONFIG['password']
    )
    
    try:
        print("1. 启动SOL连接...")
        if not explorer.start_sol_session():
            print("❌ SOL连接失败")
            return
        
        print("✅ SOL连接成功")
        time.sleep(3)
        
        print("\n2. 智能回到主页面（处理退出提示）...")
        # 使用优化的go_to_main_page，返回最后读取的内容
        main_page_content = explorer.go_to_main_page()
        
        print(f"✅ 获取主页面内容，长度: {len(main_page_content)}")
        
        # 验证是否在主页面
        if explorer._is_main_page(main_page_content):
            print("✅ 确认在主页面")
        else:
            print("⚠️  可能不在主页面")
        
        print("\n3. 提取TAB名称...")
        # 直接使用主页面内容，避免重复读取
        tab_name = explorer._extract_tab_name(main_page_content, 0)
        print(f"✅ 识别的TAB名称: {tab_name}")
        
        print(f"\n4. 显示内容预览:")
        print(f"内容长度: {len(main_page_content)}")
        print(f"内容预览: {main_page_content[:200]}...")
        
        # 保存内容用于分析
        with open("optimized_main_page.txt", "w", encoding="utf-8") as f:
            f.write(f"TAB名称: {tab_name}\n")
            f.write("=" * 50 + "\n")
            f.write(main_page_content)
        
        print(f"\n✅ 主页面内容已保存到: optimized_main_page.txt")
        
        return {
            "tab_name": tab_name,
            "content_length": len(main_page_content),
            "is_main_page": explorer._is_main_page(main_page_content)
        }
        
    except Exception as e:
        print(f"❌ 遍历失败: {e}")
        import traceback
        traceback.print_exc()
        return None
    finally:
        explorer.close_sol_session()


def optimized_tab_switching():
    """优化的TAB切换示例"""
    print("\n=== 优化的TAB切换示例 ===")
    
    explorer = BIOSMenuExplorer(
        host=SOL_CONFIG['host'],
        username=SOL_CONFIG['username'],
        password=SOL_CONFIG['password']
    )
    
    try:
        if not explorer.start_sol_session():
            print("❌ SOL连接失败")
            return
        
        print("✅ SOL连接成功")
        time.sleep(3)
        
        print("\n1. 获取第一个TAB内容...")
        # 使用优化的主页面获取
        first_tab_content = explorer.go_to_main_page()
        first_tab_name = explorer._extract_tab_name(first_tab_content, 0)
        print(f"第一个TAB: {first_tab_name} (内容长度: {len(first_tab_content)})")
        
        print("\n2. 切换到下一个TAB...")
        # 发送右箭头键切换TAB（会自动清空缓存）
        explorer.send_key(KeyCode.RIGHT, delay=1.0)
        
        # 读取新TAB内容（由于缓存被清空，会读取新内容）
        second_tab_content = explorer.read_screen_content(timeout=3.0)
        second_tab_name = explorer._extract_tab_name(second_tab_content, 1)
        print(f"第二个TAB: {second_tab_name} (内容长度: {len(second_tab_content)})")
        
        print("\n3. 验证TAB切换...")
        if first_tab_name != second_tab_name:
            print("✅ TAB切换成功，名称发生变化")
        else:
            print("⚠️  TAB名称未变化")
        
        if len(second_tab_content) > 0:
            print("✅ 成功读取到新TAB内容")
        else:
            print("❌ 未读取到新TAB内容")
        
        # 保存两个TAB的内容
        with open("first_tab_optimized.txt", "w", encoding="utf-8") as f:
            f.write(f"第一个TAB: {first_tab_name}\n")
            f.write("=" * 50 + "\n")
            f.write(first_tab_content)
        
        with open("second_tab_optimized.txt", "w", encoding="utf-8") as f:
            f.write(f"第二个TAB: {second_tab_name}\n")
            f.write("=" * 50 + "\n")
            f.write(second_tab_content)
        
        print(f"\nTAB内容已保存到:")
        print(f"  - first_tab_optimized.txt")
        print(f"  - second_tab_optimized.txt")
        
        return {
            "first_tab": {"name": first_tab_name, "length": len(first_tab_content)},
            "second_tab": {"name": second_tab_name, "length": len(second_tab_content)},
            "switch_successful": first_tab_name != second_tab_name
        }
        
    except Exception as e:
        print(f"❌ TAB切换失败: {e}")
        import traceback
        traceback.print_exc()
        return None
    finally:
        explorer.close_sol_session()


def test_cache_behavior():
    """测试缓存行为"""
    print("\n=== 测试缓存行为 ===")
    
    explorer = BIOSMenuExplorer(
        host=SOL_CONFIG['host'],
        username=SOL_CONFIG['username'],
        password=SOL_CONFIG['password']
    )
    
    try:
        if not explorer.start_sol_session():
            print("❌ SOL连接失败")
            return
        
        print("✅ SOL连接成功")
        time.sleep(3)
        
        print("\n1. 第一次读取内容（建立缓存）...")
        content1 = explorer.read_screen_content(timeout=2.0)
        print(f"读取内容长度: {len(content1)}")
        print(f"缓存状态: {len(explorer.last_screen_content)} 字符")
        
        print("\n2. 立即再次读取（应该使用缓存）...")
        content2 = explorer.read_screen_content(timeout=2.0)
        print(f"读取内容长度: {len(content2)}")
        print(f"内容一致: {content1 == content2}")
        
        print("\n3. 发送按键（应该清空缓存）...")
        print(f"发送前缓存长度: {len(explorer.last_screen_content)}")
        explorer.send_key(KeyCode.DOWN, delay=0.5)
        print(f"发送后缓存长度: {len(explorer.last_screen_content)}")
        
        print("\n4. 发送按键后读取（应该是新内容）...")
        content3 = explorer.read_screen_content(timeout=2.0)
        print(f"读取内容长度: {len(content3)}")
        print(f"与第一次内容相同: {content1 == content3}")
        
        return {
            "cache_working": content1 == content2,
            "cache_cleared": len(explorer.last_screen_content) == 0 or content1 != content3
        }
        
    except Exception as e:
        print(f"❌ 缓存测试失败: {e}")
        return None
    finally:
        explorer.close_sol_session()


def main():
    """主函数"""
    print("BIOS菜单遍历器 - 优化功能演示")
    print("=" * 50)
    
    print("主要优化:")
    print("✅ 内容缓存机制 - 避免重复读取导致的空内容")
    print("✅ go_to_main_page返回内容 - 直接使用返回的内容")
    print("✅ 智能退出提示处理 - 自动处理'Exit Without Saving'")
    print("✅ 按键后缓存清空 - 确保读取到最新内容")
    
    # 询问要运行哪个测试
    print(f"\n请选择要运行的测试:")
    print("1. 优化的单TAB遍历")
    print("2. 优化的TAB切换")
    print("3. 缓存行为测试")
    print("4. 运行所有测试")
    
    choice = input("请输入选择 (1-4): ").strip()
    
    if choice == "1":
        result = optimized_single_tab_exploration()
    elif choice == "2":
        result = optimized_tab_switching()
    elif choice == "3":
        result = test_cache_behavior()
    elif choice == "4":
        print("运行所有测试...")
        results = {}
        results["single_tab"] = optimized_single_tab_exploration()
        results["tab_switching"] = optimized_tab_switching()
        results["cache_behavior"] = test_cache_behavior()
        
        print(f"\n{'='*20} 所有测试结果 {'='*20}")
        for test_name, result in results.items():
            status = "✅ 成功" if result is not None else "❌ 失败"
            print(f"{test_name}: {status}")
        
        result = results
    else:
        print("无效选择")
        return
    
    print(f"\n{'='*20} 测试完成 {'='*20}")
    if result:
        print("✅ 测试成功完成")
        print("\n生成的文件:")
        print("- optimized_main_page.txt (主页面内容)")
        print("- first_tab_optimized.txt (第一个TAB)")
        print("- second_tab_optimized.txt (第二个TAB)")
    else:
        print("❌ 测试失败")
    
    print(f"\n优化效果:")
    print("- 解决了go_to_main_page后读取内容为空的问题")
    print("- 避免了重复读取SOL输出流")
    print("- 提高了TAB识别的准确性")
    print("- 增强了BIOS导航的稳定性")


if __name__ == "__main__":
    main()
