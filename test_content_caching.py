#!/usr/bin/env python3
"""
测试内容缓存机制
验证屏幕内容读取的缓存功能是否正常工作
"""

from bios_menu_explorer import BIOSMenuExplorer, KeyCode
from config import SOL_CONFIG
import time


def test_content_caching_logic():
    """测试内容缓存逻辑"""
    print("=== 测试内容缓存逻辑 ===")
    
    explorer = BIOSMenuExplorer("test", "test", "test")
    
    # 测试初始状态
    print("1. 测试初始状态:")
    print(f"   缓存内容: '{explorer.last_screen_content}'")
    print(f"   缓存时间: {explorer.content_cache_time}")
    
    # 模拟设置缓存
    print("\n2. 模拟设置缓存:")
    explorer.last_screen_content = "测试缓存内容"
    explorer.content_cache_time = time.time()
    print(f"   缓存内容: '{explorer.last_screen_content}'")
    print(f"   缓存时间: {explorer.content_cache_time}")
    
    # 测试清空缓存
    print("\n3. 测试清空缓存:")
    explorer.clear_content_cache()
    print(f"   缓存内容: '{explorer.last_screen_content}'")
    print(f"   缓存时间: {explorer.content_cache_time}")


def test_go_to_main_page_return():
    """测试go_to_main_page返回内容功能"""
    print("\n=== 测试go_to_main_page返回内容 ===")
    
    explorer = BIOSMenuExplorer(
        host=SOL_CONFIG['host'],
        username=SOL_CONFIG['username'],
        password=SOL_CONFIG['password']
    )
    
    try:
        print("启动SOL连接...")
        if not explorer.start_sol_session():
            print("❌ SOL连接失败")
            return
        
        print("✅ SOL连接成功")
        time.sleep(3)
        
        print("测试go_to_main_page返回内容...")
        
        # 调用go_to_main_page并获取返回的内容
        main_page_content = explorer.go_to_main_page()
        
        print(f"go_to_main_page返回内容长度: {len(main_page_content)}")
        print(f"内容预览: {main_page_content[:100]}...")
        
        # 验证缓存是否正确设置
        print(f"\n缓存验证:")
        print(f"  缓存内容长度: {len(explorer.last_screen_content)}")
        print(f"  缓存时间: {explorer.content_cache_time}")
        print(f"  内容一致性: {main_page_content == explorer.last_screen_content}")
        
        # 测试后续读取是否使用缓存
        print(f"\n测试缓存使用:")
        cached_content = explorer.read_screen_content()
        print(f"  缓存读取长度: {len(cached_content)}")
        print(f"  与返回内容一致: {cached_content == main_page_content}")
        
        return main_page_content
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None
    finally:
        explorer.close_sol_session()


def test_key_send_cache_clear():
    """测试发送按键后缓存清空"""
    print("\n=== 测试按键发送后缓存清空 ===")
    
    explorer = BIOSMenuExplorer(
        host=SOL_CONFIG['host'],
        username=SOL_CONFIG['username'],
        password=SOL_CONFIG['password']
    )
    
    try:
        if not explorer.start_sol_session():
            print("❌ SOL连接失败")
            return
        
        print("✅ SOL连接成功")
        time.sleep(3)
        
        # 先读取内容建立缓存
        print("1. 建立初始缓存:")
        initial_content = explorer.read_screen_content()
        print(f"   初始内容长度: {len(initial_content)}")
        print(f"   缓存内容长度: {len(explorer.last_screen_content)}")
        
        # 发送按键（应该清空缓存）
        print("\n2. 发送按键（应该清空缓存）:")
        print(f"   发送前缓存长度: {len(explorer.last_screen_content)}")
        explorer.send_key(KeyCode.RIGHT, delay=0.5)
        print(f"   发送后缓存长度: {len(explorer.last_screen_content)}")
        
        # 再次读取内容
        print("\n3. 发送按键后读取新内容:")
        new_content = explorer.read_screen_content()
        print(f"   新内容长度: {len(new_content)}")
        print(f"   与初始内容相同: {new_content == initial_content}")
        
        # 保存内容用于对比
        with open("initial_content.txt", "w", encoding="utf-8") as f:
            f.write("初始内容:\n")
            f.write("=" * 50 + "\n")
            f.write(initial_content)
        
        with open("after_key_content.txt", "w", encoding="utf-8") as f:
            f.write("按键后内容:\n")
            f.write("=" * 50 + "\n")
            f.write(new_content)
        
        print("内容已保存到 initial_content.txt 和 after_key_content.txt")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        explorer.close_sol_session()


def test_tab_exploration_with_caching():
    """测试带缓存的TAB遍历"""
    print("\n=== 测试带缓存的TAB遍历 ===")
    
    explorer = BIOSMenuExplorer(
        host=SOL_CONFIG['host'],
        username=SOL_CONFIG['username'],
        password=SOL_CONFIG['password']
    )
    
    try:
        if not explorer.start_sol_session():
            print("❌ SOL连接失败")
            return
        
        print("✅ SOL连接成功")
        time.sleep(3)
        
        print("开始TAB遍历测试...")
        
        # 模拟explore_all_tabs的逻辑
        print("1. 回到主页面并获取内容:")
        main_page_content = explorer.go_to_main_page()
        print(f"   主页面内容长度: {len(main_page_content)}")
        
        # 模拟第一个TAB（使用主页面内容）
        print("\n2. 处理第一个TAB (使用主页面内容):")
        tab_content = main_page_content
        print(f"   TAB内容长度: {len(tab_content)}")
        print(f"   内容非空: {len(tab_content) > 0}")
        
        # 提取TAB名称
        tab_name = explorer._extract_tab_name(tab_content, 0)
        print(f"   识别的TAB名称: {tab_name}")
        
        # 模拟切换到下一个TAB
        print("\n3. 切换到下一个TAB:")
        print(f"   切换前缓存长度: {len(explorer.last_screen_content)}")
        explorer.send_key(KeyCode.RIGHT, delay=1.0)
        print(f"   切换后缓存长度: {len(explorer.last_screen_content)}")
        
        # 读取新TAB内容
        new_tab_content = explorer.read_screen_content()
        print(f"   新TAB内容长度: {len(new_tab_content)}")
        
        new_tab_name = explorer._extract_tab_name(new_tab_content, 1)
        print(f"   新TAB名称: {new_tab_name}")
        
        print(f"\n4. 结果对比:")
        print(f"   第一个TAB: {tab_name}")
        print(f"   第二个TAB: {new_tab_name}")
        print(f"   TAB名称不同: {tab_name != new_tab_name}")
        
        return {
            "first_tab": {"name": tab_name, "content_length": len(tab_content)},
            "second_tab": {"name": new_tab_name, "content_length": len(new_tab_content)}
        }
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None
    finally:
        explorer.close_sol_session()


def main():
    """主测试函数"""
    print("BIOS内容缓存机制测试")
    print("=" * 40)
    
    # 运行离线测试
    test_content_caching_logic()
    
    # 询问是否运行真实连接测试
    response = input("\n是否运行真实BIOS连接测试? (y/n): ").strip().lower()
    if response == 'y':
        # 运行真实连接测试
        tests = [
            ("go_to_main_page返回内容测试", test_go_to_main_page_return),
            ("按键发送缓存清空测试", test_key_send_cache_clear),
            ("带缓存的TAB遍历测试", test_tab_exploration_with_caching)
        ]
        
        results = {}
        for test_name, test_func in tests:
            print(f"\n{'='*20} {test_name} {'='*20}")
            try:
                result = test_func()
                results[test_name] = result
                print(f"✅ {test_name} 完成")
            except Exception as e:
                print(f"❌ {test_name} 失败: {e}")
                results[test_name] = None
            
            time.sleep(1)  # 测试间隔
        
        # 显示总结
        print(f"\n{'='*20} 测试总结 {'='*20}")
        for test_name, result in results.items():
            status = "✅ 成功" if result is not None else "❌ 失败"
            print(f"{test_name}: {status}")
    
    print("\n测试完成！")
    print("\n主要改进:")
    print("✅ 内容缓存机制避免重复读取")
    print("✅ go_to_main_page返回最后读取的内容")
    print("✅ 发送按键后自动清空缓存")
    print("✅ 智能缓存使用逻辑")


if __name__ == "__main__":
    main()
