# SOL断开功能指南

## 概述

为了更好地管理SOL (Serial Over LAN) 连接，我添加了完善的SOL断开和deactivate功能，确保连接能够正确关闭，避免资源泄漏和连接冲突。

## 新增功能

### 1. 增强的SOL关闭功能

```python
def close_sol_session(self):
    """关闭SOL会话"""
    if self.sol_process:
        try:
            # 首先尝试发送SOL deactivate命令
            print("发送SOL deactivate命令...")
            self._send_sol_deactivate()
            
            # 等待进程正常结束
            print("等待SOL进程结束...")
            self.sol_process.wait(timeout=5)
            
        except subprocess.TimeoutExpired:
            # 超时则强制终止
            self.sol_process.terminate()
            # 如果还不行就强制杀死
            self.sol_process.kill()
```

### 2. SOL Deactivate命令

```python
def deactivate_sol(self):
    """使用ipmitool命令deactivate SOL"""
    cmd = ['ipmitool', '-I', 'lanplus', '-H', self.host,
           '-U', self.username, '-P', self.password,
           'sol', 'deactivate']
    
    result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
```

### 3. 强制断开功能

```python
def force_disconnect_sol(self):
    """强制断开SOL连接"""
    # 1. 首先尝试deactivate命令
    self.deactivate_sol()
    
    # 2. 如果进程仍在运行，强制终止
    if self.sol_process and self.sol_process.poll() is None:
        self.sol_process.terminate()
        # 最后手段：强制杀死
        self.sol_process.kill()
```

### 4. SOL断开序列

```python
def _send_sol_deactivate(self):
    """发送SOL deactivate命令"""
    # SOL会话中，使用 ~. 序列来断开连接
    self.sol_process.stdin.write('~.')
    self.sol_process.stdin.flush()
```

## 断开方法层次

### 层次1: 正常断开
- 发送 `~.` 断开序列
- 等待进程自然结束
- **适用场景**: 正常操作完成后

### 层次2: 命令断开
- 执行 `ipmitool sol deactivate` 命令
- 从外部终止SOL会话
- **适用场景**: 断开序列无效时

### 层次3: 进程终止
- 发送 `SIGTERM` 信号
- 等待进程响应并退出
- **适用场景**: deactivate命令无效时

### 层次4: 强制杀死
- 发送 `SIGKILL` 信号
- 立即终止进程
- **适用场景**: 所有其他方法都失败时

## 使用方法

### 基本使用

```python
explorer = BIOSMenuExplorer(host, username, password)

try:
    # 启动SOL连接
    explorer.start_sol_session()
    
    # 执行BIOS操作
    content = explorer.go_to_main_page()
    
finally:
    # 正常关闭（推荐）
    explorer.close_sol_session()
```

### 异常处理

```python
try:
    explorer.start_sol_session()
    # ... 执行操作 ...
    
except Exception as e:
    print(f"操作失败: {e}")
    # 强制断开
    explorer.force_disconnect_sol()
```

### 中断信号处理

```python
import signal
import sys

def cleanup_handler(signum, frame):
    print("收到中断信号，清理SOL连接...")
    explorer.force_disconnect_sol()
    sys.exit(0)

signal.signal(signal.SIGINT, cleanup_handler)
signal.signal(signal.SIGTERM, cleanup_handler)
```

### 完整遍历（自动清理）

```python
# run_full_exploration 已包含完整的清理逻辑
explorer = BIOSMenuExplorer(host, username, password)
menu_structure = explorer.run_full_exploration()
# 自动处理所有清理工作
```

## 改进的run_full_exploration

```python
def run_full_exploration(self) -> Dict:
    try:
        # ... 遍历逻辑 ...
        return menu_structure
        
    except KeyboardInterrupt:
        print("\n用户中断遍历过程")
        return {}
    except Exception as e:
        print(f"遍历过程中出错: {e}")
        return {}
    finally:
        # 确保SOL连接被正确关闭
        try:
            self.close_sol_session()
        except Exception as e:
            # 如果正常关闭失败，尝试强制断开
            self.force_disconnect_sol()
```

## 测试和验证

### 运行测试

```bash
# 测试SOL断开功能
python test_sol_disconnect.py

# 使用示例
python sol_disconnect_example.py
```

### 测试内容

1. **正常断开测试** - 验证正常的SOL关闭流程
2. **强制断开测试** - 验证强制断开功能
3. **Deactivate命令测试** - 验证外部deactivate命令
4. **多次连接测试** - 验证重复连接和断开
5. **中断处理测试** - 验证信号处理和清理

## 故障排除

### 常见问题

1. **SOL进程无法终止**
   ```python
   # 使用强制断开
   explorer.force_disconnect_sol()
   ```

2. **连接冲突**
   ```bash
   # 手动deactivate所有SOL会话
   ipmitool -I lanplus -H <host> -U <user> -P <pass> sol deactivate
   ```

3. **进程僵尸**
   ```python
   # 检查进程状态
   if explorer.sol_process:
       print(f"进程状态: {explorer.sol_process.poll()}")
   ```

### 调试命令

```bash
# 检查SOL状态
ipmitool -I lanplus -H <host> -U <user> -P <pass> sol info

# 查看活动的SOL会话
ps aux | grep ipmitool

# 强制终止所有ipmitool进程
pkill -f ipmitool
```

## 最佳实践

### 1. 总是使用try-finally

```python
explorer = BIOSMenuExplorer(host, username, password)
try:
    explorer.start_sol_session()
    # ... 操作 ...
finally:
    explorer.close_sol_session()
```

### 2. 处理中断信号

```python
import signal

def cleanup_handler(signum, frame):
    explorer.force_disconnect_sol()
    sys.exit(0)

signal.signal(signal.SIGINT, cleanup_handler)
```

### 3. 检查连接状态

```python
if explorer.sol_process and explorer.sol_process.poll() is None:
    print("SOL连接活跃")
else:
    print("SOL连接已断开")
```

### 4. 使用超时机制

```python
try:
    explorer.sol_process.wait(timeout=5)
except subprocess.TimeoutExpired:
    explorer.sol_process.kill()
```

## 总结

新的SOL断开功能提供了：

✅ **多层次断开机制** - 从温和到强制的完整断开流程
✅ **异常安全** - 确保在任何情况下都能正确清理
✅ **中断处理** - 响应用户中断和系统信号
✅ **资源管理** - 避免SOL连接泄漏和冲突
✅ **调试支持** - 详细的状态信息和错误处理

这些改进确保了BIOS菜单遍历器能够可靠地管理SOL连接，避免了连接冲突和资源泄漏问题。
