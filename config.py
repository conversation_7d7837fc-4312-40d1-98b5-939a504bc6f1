#!/usr/bin/env python3
"""
BIOS菜单遍历器配置文件
包含连接参数、遍历设置和解析规则
"""

# SOL连接配置
SOL_CONFIG = {
    "host": "************",
    "username": "Administrator", 
    "password": "Superuser9!",
    "interface": "lanplus",
    "timeout": 30,
    "retry_count": 3
}

# 遍历设置
EXPLORATION_CONFIG = {
    "max_depth": 8,              # 最大菜单深度
    "max_items_per_menu": 50,    # 每个菜单最大项目数
    "max_tabs": 8,              # 最大TAB数量
    "navigation_delay": 0.5,     # 导航延迟(秒)
    "read_timeout": 2.0,         # 读取超时(秒)
    "stability_wait": 1.0,       # 等待界面稳定时间(秒)
    "similarity_threshold": 0.7   # 内容相似度阈值
}

# 按键延迟配置
KEY_DELAYS = {
    "navigation": 0.5,    # 导航按键延迟
    "enter": 0.8,         # 回车键延迟
    "escape": 0.3,        # ESC键延迟
    "tab_switch": 0.6     # TAB切换延迟
}

# BIOS界面解析规则
PARSING_RULES = {
    # TAB名称识别关键词
    "tab_keywords": [
        "main", "advanced", "security", "boot", "exit", 
        "chipset", "power", "monitoring", "overclocking"
    ],
    
    # 菜单项识别模式
    "menu_patterns": [
        r"^\s*[\[\]>]\s*.+",           # 以 [ ] > 开头的行
        r"^\s*.+:\s*.+",               # 包含冒号的配置项
        r"^\s*.+\s+\[.+\]",            # 末尾有方括号的项
        r"^\s*\d+\.\s*.+",             # 数字编号的项
        r"^\s*[A-Z][a-zA-Z\s]+$"       # 纯字母的菜单项
    ],
    
    # 需要忽略的行模式
    "ignore_patterns": [
        r"^\s*$",                      # 空行
        r"^\s*[-=+*]{3,}",             # 分隔线
        r"^\s*Press.*key",             # 按键提示
        r"^\s*F\d+",                   # 功能键提示
        r"^\s*ESC.*Exit",              # ESC退出提示
        r"^\s*\d{2}:\d{2}:\d{2}",      # 时间戳
        r"^\s*Version\s+\d+",          # 版本信息
        r"^\s*Copyright"               # 版权信息
    ],
    
    # 子菜单识别关键词
    "submenu_indicators": [
        "submenu", "setup", "configuration", "options", 
        "settings", "parameters", "advanced", "details"
    ],
    
    # 配置项识别关键词
    "config_indicators": [
        "enabled", "disabled", "auto", "manual", "on", "off",
        "yes", "no", "true", "false", "[", "]", ":", "="
    ]
}

# 输出格式配置
OUTPUT_CONFIG = {
    "json_indent": 2,
    "encoding": "utf-8",
    "include_content": True,      # 是否包含页面内容
    "include_timestamps": True,   # 是否包含时间戳
    "compress_content": False,    # 是否压缩内容
    "max_content_length": 5000    # 最大内容长度
}

# 日志配置
LOGGING_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(levelname)s - %(message)s",
    "file": "bios_explorer.log",
    "console": True
}

# 错误处理配置
ERROR_HANDLING = {
    "max_retries": 3,
    "retry_delay": 1.0,
    "continue_on_error": True,
    "save_partial_results": True
}

# 性能优化配置
PERFORMANCE_CONFIG = {
    "use_cache": True,
    "cache_timeout": 300,         # 缓存超时(秒)
    "parallel_processing": False,  # 是否并行处理
    "memory_limit": 100 * 1024 * 1024  # 内存限制(字节)
}


def get_sol_command(config=None):
    """生成SOL命令"""
    if config is None:
        config = SOL_CONFIG
    
    return [
        'ipmitool', 
        '-I', config['interface'],
        '-H', config['host'],
        '-U', config['username'],
        '-P', config['password'],
        'sol', 'activate'
    ]


def validate_config():
    """验证配置参数"""
    errors = []
    
    # 验证SOL配置
    required_sol_keys = ['host', 'username', 'password']
    for key in required_sol_keys:
        if not SOL_CONFIG.get(key):
            errors.append(f"SOL配置缺少必需参数: {key}")
    
    # 验证遍历配置
    if EXPLORATION_CONFIG['max_depth'] < 1:
        errors.append("max_depth必须大于0")
    
    if EXPLORATION_CONFIG['navigation_delay'] < 0:
        errors.append("navigation_delay不能为负数")
    
    # 验证按键延迟
    for key, delay in KEY_DELAYS.items():
        if delay < 0:
            errors.append(f"按键延迟 {key} 不能为负数")
    
    return errors


def get_config_summary():
    """获取配置摘要"""
    return {
        "sol_host": SOL_CONFIG['host'],
        "max_depth": EXPLORATION_CONFIG['max_depth'],
        "max_tabs": EXPLORATION_CONFIG['max_tabs'],
        "navigation_delay": EXPLORATION_CONFIG['navigation_delay'],
        "similarity_threshold": EXPLORATION_CONFIG['similarity_threshold'],
        "include_content": OUTPUT_CONFIG['include_content']
    }


if __name__ == "__main__":
    print("BIOS遍历器配置验证")
    
    errors = validate_config()
    if errors:
        print("配置错误:")
        for error in errors:
            print(f"  - {error}")
    else:
        print("配置验证通过")
    
    print("\n配置摘要:")
    summary = get_config_summary()
    for key, value in summary.items():
        print(f"  {key}: {value}")
