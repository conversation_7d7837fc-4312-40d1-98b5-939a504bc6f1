#!/usr/bin/env python3
"""
BIOS菜单结构遍历器
基于SOL (Serial Over LAN) 实现BIOS菜单的自动遍历和结构获取
"""

import os
import time
import fcntl
import select
import subprocess
import json
import re
from typing import Dict, List
from dataclasses import dataclass, asdict
from enum import Enum


class KeyCode(Enum):
    """BIOS导航按键定义"""
    UP = '\x1b[A'
    DOWN = '\x1b[B'
    RIGHT = '\x1b[C'
    LEFT = '\x1b[D'
    ENTER = '\r'
    ESC = '\x1b'
    TAB = '\t'
    F1 = '\x1bOP'
    F2 = '\x1bOQ'
    F10 = '\x1b[21~'


@dataclass
class MenuNode:
    """BIOS菜单节点数据结构"""
    name: str
    path: str  # 从根节点到当前节点的路径
    level: int
    tab_index: int  # 所属TAB页索引
    parent_path: str
    children: List['MenuNode']
    content: str  # 页面内容
    is_submenu: bool = False
    is_configurable: bool = False


class BIOSMenuExplorer:
    """BIOS菜单遍历器"""
    
    def __init__(self, host: str, username: str, password: str):
        self.host = host
        self.username = username
        self.password = password
        self.sol_process = None
        self.menu_structure = {}
        self.current_path = []
        self.visited_paths = set()
        self.tab_count = 0
        self.current_tab = 0
        
    def start_sol_session(self) -> bool:
        """启动SOL会话"""
        cmd = ['ipmitool', '-I', 'lanplus', '-H', self.host,
               '-U', self.username, '-P', self.password,
               'sol', 'activate']
        
        print("启动SOL进程...")
        try:
            self.sol_process = subprocess.Popen(
                cmd,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            print("等待SOL连接建立...")
            time.sleep(3)
            
            if self.sol_process.poll() is not None:
                _, stderr = self.sol_process.communicate()
                print(f"SOL进程启动失败，返回码: {self.sol_process.returncode}")
                print(f"错误信息: {stderr}")
                return False
                
            # 设置非阻塞IO
            self._setup_nonblocking_io()
            print("SOL会话启动成功")
            return True
            
        except Exception as e:
            print(f"启动SOL会话时出错: {e}")
            return False
    
    def _setup_nonblocking_io(self):
        """设置非阻塞IO"""
        fd_out = self.sol_process.stdout.fileno()
        fd_err = self.sol_process.stderr.fileno()
        
        fl_out = fcntl.fcntl(fd_out, fcntl.F_GETFL)
        fcntl.fcntl(fd_out, fcntl.F_SETFL, fl_out | os.O_NONBLOCK)
        
        fl_err = fcntl.fcntl(fd_err, fcntl.F_GETFL)
        fcntl.fcntl(fd_err, fcntl.F_SETFL, fl_err | os.O_NONBLOCK)
    
    def send_key(self, key: KeyCode, delay: float = 0.5) -> bool:
        """发送按键到BIOS"""
        if not self.sol_process or self.sol_process.poll() is not None:
            print("SOL进程未运行")
            return False
            
        try:
            self.sol_process.stdin.write(key.value)
            self.sol_process.stdin.flush()
            time.sleep(delay)
            return True
        except Exception as e:
            print(f"发送按键失败: {e}")
            return False
    
    def read_screen_content(self, timeout: float = 2.0) -> str:
        """读取当前屏幕内容"""
        if not self.sol_process:
            return ""
            
        outputs = []
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            if self.sol_process.poll() is not None:
                break
                
            ready, _, _ = select.select([self.sol_process.stdout], [], [], 0.1)
            if not ready:
                continue
                
            try:
                fd = self.sol_process.stdout.fileno()
                data = os.read(fd, 4096)
                if data:
                    outputs.append(data.decode('utf-8', errors='ignore'))
            except Exception as e:
                print(f"读取屏幕内容时出错: {e}")
                break
        
        return ''.join(outputs)
    
    def detect_tab_count(self) -> int:
        """检测TAB页数量"""
        print("检测BIOS TAB页数量...")
        
        # 先回到主页面
        self.go_to_main_page()
        
        initial_content = self.read_screen_content()
        tab_count = 1
        
        # 尝试向右切换TAB，直到回到初始页面
        for _ in range(10):  # 最多检测10个TAB
            self.send_key(KeyCode.RIGHT)
            current_content = self.read_screen_content()
            
            if self._is_similar_content(initial_content, current_content):
                break
            tab_count += 1
        
        print(f"检测到 {tab_count} 个TAB页")
        self.tab_count = tab_count
        return tab_count

    def _is_similar_content(self, content1: str, content2: str) -> bool:
        """判断两个内容是否相似（用于检测是否回到初始TAB）"""
        # 简单的相似度检测，可以根据实际情况优化
        if len(content1) == 0 or len(content2) == 0:
            return False
        
        # 提取关键字进行比较
        words1 = set(re.findall(r'\w+', content1.lower()))
        words2 = set(re.findall(r'\w+', content2.lower()))
        
        if len(words1) == 0 or len(words2) == 0:
            return False
            
        intersection = words1.intersection(words2)
        similarity = len(intersection) / max(len(words1), len(words2))
        
        return similarity > 0.7  # 70%相似度阈值
    
    def go_to_main_page(self):
        """回到BIOS主页面"""
        print("回到BIOS主页面...")
        
        # 多次按ESC确保回到主页面
        for _ in range(8):
            self.send_key(KeyCode.ESC, 0.3)
        
        # 清空当前路径
        self.current_path = []
    
    def explore_all_tabs(self) -> Dict:
        """遍历所有TAB页"""
        print("开始遍历所有TAB页...")
        
        if self.tab_count == 0:
            self.detect_tab_count()
        
        all_tabs_structure = {}
        
        for tab_index in range(self.tab_count):
            print(f"\n=== 遍历TAB {tab_index + 1}/{self.tab_count} ===")
            
            # 切换到指定TAB
            if tab_index > 0:
                for _ in range(tab_index):
                    self.send_key(KeyCode.RIGHT)
            
            self.current_tab = tab_index
            
            # 获取当前TAB的内容和结构
            tab_content = self.read_screen_content()
            tab_name = self._extract_tab_name(tab_content, tab_index)
            
            print(f"当前TAB: {tab_name}")
            
            # 遍历当前TAB的菜单结构
            tab_structure = self.explore_current_tab(tab_name, tab_index)
            all_tabs_structure[tab_name] = tab_structure
            
            # 回到主页面准备下一个TAB
            self.go_to_main_page()
        
        return all_tabs_structure
    
    def _extract_tab_name(self, content: str, tab_index: int) -> str:
        """从内容中提取TAB名称"""
        # 这里需要根据实际BIOS界面格式来解析TAB名称
        # 简单实现，可以根据实际情况优化
        lines = content.split('\n')
        for line in lines[:10]:  # 检查前10行
            line = line.strip()
            if line and len(line) < 50:  # 假设TAB名称不会太长
                # 可以添加更多的启发式规则来识别TAB名称
                if any(keyword in line.lower() for keyword in ['main', 'advanced', 'platform configuration', 'socket configuration', 'server mgmt', 'security', 'boot', 'save & exit']):
                    return line
        
        return f"Tab_{tab_index + 1}"
    
    def explore_current_tab(self, tab_name: str, tab_index: int) -> Dict:
        """遍历当前TAB的菜单结构"""
        print(f"遍历TAB: {tab_name}")
        
        root_content = self.read_screen_content()
        
        # 创建根节点
        root_node = MenuNode(
            name=tab_name,
            path=f"/{tab_name}",
            level=0,
            tab_index=tab_index,
            parent_path="",
            children=[],
            content=root_content
        )
        
        # 递归遍历子菜单
        self._explore_submenu(root_node)
        
        return asdict(root_node)
    
    def _explore_submenu(self, parent_node: MenuNode):
        """递归遍历子菜单"""
        print(f"遍历子菜单: {parent_node.path}")
        
        # 防止无限递归
        if parent_node.level > 5:  # 最大深度限制
            return
        
        if parent_node.path in self.visited_paths:
            return
        
        self.visited_paths.add(parent_node.path)
        
        # 尝试向下导航，寻找菜单项
        menu_items = self._find_menu_items()
        
        for item_index, item_name in enumerate(menu_items):
            # 导航到菜单项
            self._navigate_to_item(item_index)
            
            # 尝试进入子菜单
            if self._try_enter_submenu():
                # 成功进入子菜单
                submenu_content = self.read_screen_content()
                
                child_node = MenuNode(
                    name=item_name,
                    path=f"{parent_node.path}/{item_name}",
                    level=parent_node.level + 1,
                    tab_index=parent_node.tab_index,
                    parent_path=parent_node.path,
                    children=[],
                    content=submenu_content,
                    is_submenu=True
                )
                
                parent_node.children.append(child_node)
                
                # 递归遍历子菜单
                self._explore_submenu(child_node)
                
                # 返回上级菜单
                self.send_key(KeyCode.ESC)
        
    def _find_menu_items(self) -> List[str]:
        """查找当前页面的菜单项"""
        content = self.read_screen_content()
        
        # 这里需要根据实际BIOS界面格式来解析菜单项
        # 简单实现，寻找可能的菜单项
        lines = content.split('\n')
        menu_items = []
        
        for line in lines:
            line = line.strip()
            if line and len(line) < 100:  # 过滤掉太长的行
                # 简单的启发式规则识别菜单项
                if any(char in line for char in ['>', '[', ']', ':']):
                    menu_items.append(line)
        
        return menu_items[:20]  # 限制菜单项数量
    
    def _navigate_to_item(self, item_index: int):
        """导航到指定的菜单项"""
        # 先回到菜单顶部
        for _ in range(20):
            self.send_key(KeyCode.UP, 0.1)
        
        # 向下导航到指定项
        for _ in range(item_index):
            self.send_key(KeyCode.DOWN, 0.1)
    
    def _try_enter_submenu(self) -> bool:
        """尝试进入子菜单"""
        before_content = self.read_screen_content()
        
        # 发送回车键
        self.send_key(KeyCode.ENTER)
        
        after_content = self.read_screen_content()
        
        # 判断是否成功进入子菜单（内容发生变化）
        return not self._is_similar_content(before_content, after_content)
    
    def save_menu_structure(self, structure: Dict, filename: str = "bios_menu_structure.json"):
        """保存菜单结构到文件"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(structure, f, ensure_ascii=False, indent=2)
            print(f"菜单结构已保存到: {filename}")
        except Exception as e:
            print(f"保存菜单结构失败: {e}")
    
    def close_sol_session(self):
        """关闭SOL会话"""
        if self.sol_process:
            try:
                self.sol_process.terminate()
                self.sol_process.wait(timeout=5)
            except:
                self.sol_process.kill()
            finally:
                self.sol_process = None
        print("SOL会话已关闭")
    
    def run_full_exploration(self) -> Dict:
        """运行完整的BIOS菜单遍历"""
        print("开始BIOS菜单结构遍历...")
        
        try:
            if not self.start_sol_session():
                return {}
            
            # 等待BIOS界面稳定
            time.sleep(2)
            
            # 遍历所有TAB页
            menu_structure = self.explore_all_tabs()
            
            # 保存结果
            self.save_menu_structure(menu_structure)
            
            return menu_structure
            
        except Exception as e:
            print(f"遍历过程中出错: {e}")
            return {}
        finally:
            self.close_sol_session()


def main():
    """主函数"""
    # 配置参数（从simple_git_bios_info.py中获取）
    HOST = "************"
    USERNAME = "Administrator"
    PASSWORD = "Superuser9!"
    
    # 创建遍历器实例
    explorer = BIOSMenuExplorer(HOST, USERNAME, PASSWORD)
    
    # 运行完整遍历
    menu_structure = explorer.run_full_exploration()
    
    if menu_structure:
        print("\n=== BIOS菜单结构遍历完成 ===")
        print(f"发现 {len(menu_structure)} 个主要TAB页")
        
        for tab_name, tab_data in menu_structure.items():
            print(f"\nTAB: {tab_name}")
            print(f"  子菜单数量: {len(tab_data.get('children', []))}")
    else:
        print("菜单结构遍历失败")


if __name__ == "__main__":
    main()
