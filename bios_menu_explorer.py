#!/usr/bin/env python3
"""
BIOS菜单结构遍历器
基于SOL (Serial Over LAN) 实现BIOS菜单的自动遍历和结构获取
"""

import os
import time
import fcntl
import select
import subprocess
import json
import re
from typing import Dict, List
from dataclasses import dataclass, asdict
from enum import Enum


class KeyCode(Enum):
    """BIOS导航按键定义"""
    UP = '\x1b[A'
    DOWN = '\x1b[B'
    RIGHT = '\x1b[C'
    LEFT = '\x1b[D'
    ENTER = '\r'
    ESC = '\x1b'
    TAB = '\t'
    F1 = '\x1bOP'
    F2 = '\x1bOQ'
    F10 = '\x1b[21~'


@dataclass
class MenuNode:
    """BIOS菜单节点数据结构"""
    name: str
    path: str  # 从根节点到当前节点的路径
    level: int
    tab_index: int  # 所属TAB页索引
    parent_path: str
    children: List['MenuNode']
    content: str  # 页面内容
    is_submenu: bool = False
    is_configurable: bool = False


class BIOSMenuExplorer:
    """BIOS菜单遍历器"""
    
    def __init__(self, host: str, username: str, password: str):
        self.host = host
        self.username = username
        self.password = password
        self.sol_process = None
        self.menu_structure = {}
        self.current_path = []
        self.visited_paths = set()
        self.tab_count = 0
        self.current_tab = 0
        self.last_screen_content = ""  # 缓存最后读取的屏幕内容
        self.content_cache_time = 0    # 内容缓存时间戳
        
    def start_sol_session(self) -> bool:
        """启动SOL会话"""
        cmd = ['ipmitool', '-I', 'lanplus', '-H', self.host,
               '-U', self.username, '-P', self.password,
               'sol', 'activate']
        
        print("启动SOL进程...")
        try:
            self.sol_process = subprocess.Popen(
                cmd,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            print("等待SOL连接建立...")
            time.sleep(3)
            
            if self.sol_process.poll() is not None:
                _, stderr = self.sol_process.communicate()
                print(f"SOL进程启动失败，返回码: {self.sol_process.returncode}")
                print(f"错误信息: {stderr}")
                return False
                
            # 设置非阻塞IO
            self._setup_nonblocking_io()
            print("SOL会话启动成功")
            return True
            
        except Exception as e:
            print(f"启动SOL会话时出错: {e}")
            return False
    
    def _setup_nonblocking_io(self):
        """设置非阻塞IO"""
        fd_out = self.sol_process.stdout.fileno()
        fd_err = self.sol_process.stderr.fileno()
        
        fl_out = fcntl.fcntl(fd_out, fcntl.F_GETFL)
        fcntl.fcntl(fd_out, fcntl.F_SETFL, fl_out | os.O_NONBLOCK)
        
        fl_err = fcntl.fcntl(fd_err, fcntl.F_GETFL)
        fcntl.fcntl(fd_err, fcntl.F_SETFL, fl_err | os.O_NONBLOCK)
    
    def send_key(self, key: KeyCode, delay: float = 0.5) -> bool:
        """发送按键到BIOS"""
        if not self.sol_process or self.sol_process.poll() is not None:
            print("SOL进程未运行")
            return False

        try:
            self.sol_process.stdin.write(key.value)
            self.sol_process.stdin.flush()
            time.sleep(delay)

            # 发送按键后清空内容缓存，确保下次读取新内容
            self.clear_content_cache()

            return True
        except Exception as e:
            print(f"发送按键失败: {e}")
            return False
    
    def read_screen_content(self, timeout: float = 2.0, force_read: bool = False) -> str:
        """读取当前屏幕内容，支持缓存避免重复读取"""
        if not self.sol_process:
            return ""

        # 如果不强制读取且有缓存内容，检查是否可以使用缓存
        current_time = time.time()
        if not force_read and self.last_screen_content and (current_time - self.content_cache_time) < 1.0:
            print("使用缓存的屏幕内容")
            return self.last_screen_content

        outputs = []
        start_time = time.time()

        while time.time() - start_time < timeout:
            if self.sol_process.poll() is not None:
                break

            ready, _, _ = select.select([self.sol_process.stdout], [], [], 0.1)
            if not ready:
                continue

            try:
                fd = self.sol_process.stdout.fileno()
                data = os.read(fd, 4096)
                if data:
                    outputs.append(data.decode('utf-8', errors='ignore'))
            except Exception as e:
                print(f"读取屏幕内容时出错: {e}")
                break

        content = ''.join(outputs)

        # 更新缓存
        if content:  # 只有当读取到内容时才更新缓存
            self.last_screen_content = content
            self.content_cache_time = current_time
            print(f"读取到新内容，长度: {len(content)}")
        elif self.last_screen_content:
            print("未读取到新内容，使用缓存内容")
            content = self.last_screen_content

        return content

    def clear_content_cache(self):
        """清空内容缓存，强制下次读取新内容"""
        self.last_screen_content = ""
        self.content_cache_time = 0
        print("已清空内容缓存")

    def debug_screen_content(self, content: str, save_to_file: bool = True) -> None:
        """调试屏幕内容，分析ANSI转义字符"""
        import re

        print("\n=== 屏幕内容调试信息 ===")
        print(f"原始内容长度: {len(content)}")

        # 保存原始内容到文件
        if save_to_file:
            with open("debug_raw_content.txt", "w", encoding="utf-8") as f:
                f.write(content)
            print("原始内容已保存到: debug_raw_content.txt")

        # 查找ANSI转义序列
        ansi_pattern = re.compile(r'\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])')
        ansi_sequences = ansi_pattern.findall(content)

        if ansi_sequences:
            print(f"发现 {len(ansi_sequences)} 个ANSI转义序列:")
            unique_sequences = list(set(ansi_sequences))
            for seq in unique_sequences[:10]:  # 只显示前10个不同的序列
                print(f"  {repr(seq)}")

        # 清理ANSI序列后的内容
        clean_content = ansi_pattern.sub('', content)
        print(f"清理后内容长度: {len(clean_content)}")

        # 保存清理后的内容
        if save_to_file:
            with open("debug_clean_content.txt", "w", encoding="utf-8") as f:
                f.write(clean_content)
            print("清理后内容已保存到: debug_clean_content.txt")

        # 分析前20行内容
        lines = clean_content.split('\n')
        print(f"\n前20行内容分析:")
        for i, line in enumerate(lines[:20]):
            if line.strip():
                print(f"  行{i+1:2d}: {repr(line[:80])}")  # 只显示前80个字符

        # 查找可能的TAB标识
        print(f"\n可能的TAB标识:")
        tab_keywords = ['main', 'advanced', 'platform configuration', 'socket configuration',
                       'server mgmt', 'security', 'boot', 'save & exit']

        for i, line in enumerate(lines[:15]):
            line_lower = line.lower()
            for keyword in tab_keywords:
                if keyword in line_lower:
                    print(f"  行{i+1}: 发现关键词 '{keyword}' -> {repr(line.strip())}")

        print("=== 调试信息结束 ===\n")
    
    def detect_tab_count(self) -> int:
        """检测TAB页数量"""
        print("检测BIOS TAB页数量...")
        
        # 先回到主页面
        self.go_to_main_page()
        
        initial_content = self.read_screen_content()
        tab_count = 1
        
        # 尝试向右切换TAB，直到回到初始页面
        for _ in range(10):  # 最多检测10个TAB
            self.send_key(KeyCode.RIGHT)
            current_content = self.read_screen_content()
            
            if self._is_similar_content(initial_content, current_content):
                break
            tab_count += 1
        
        print(f"检测到 {tab_count} 个TAB页")
        self.tab_count = tab_count
        return tab_count

    def _is_similar_content(self, content1: str, content2: str) -> bool:
        """判断两个内容是否相似（用于检测是否回到初始TAB）"""
        # 简单的相似度检测，可以根据实际情况优化
        if len(content1) == 0 or len(content2) == 0:
            return False
        
        # 提取关键字进行比较
        words1 = set(re.findall(r'\w+', content1.lower()))
        words2 = set(re.findall(r'\w+', content2.lower()))
        
        if len(words1) == 0 or len(words2) == 0:
            return False
            
        intersection = words1.intersection(words2)
        similarity = len(intersection) / max(len(words1), len(words2))
        
        return similarity > 0.7  # 70%相似度阈值
    
    def go_to_main_page(self):
        """回到BIOS主页面，智能处理退出提示"""
        print("回到BIOS主页面...")

        max_attempts = 8
        final_content = ""

        for attempt in range(max_attempts):
            # 读取当前屏幕内容
            content = self.read_screen_content(timeout=1.0)
            final_content = content  # 保存最后读取的内容

            # 检查是否出现退出提示
            if self._check_exit_prompt(content):
                print("检测到退出提示 'Exit Without Saving'，按ESC消除提示")
                self.send_key(KeyCode.ESC, 0.5)
                time.sleep(0.5)

                # 再次检查是否已经在主页面
                content_after = self.read_screen_content(timeout=1.0)
                final_content = content_after
                if self._is_main_page(content_after):
                    print("✅ 已成功回到主页面")
                    break
            elif self._is_main_page(content):
                print("✅ 已在主页面")
                break
            else:
                # 继续按ESC返回上级
                print(f"尝试 {attempt + 1}/{max_attempts}: 按ESC返回上级")
                self.send_key(KeyCode.ESC, 0.3)
                time.sleep(0.3)

        # 清空当前路径
        self.current_path = []

        # 返回最后读取的内容，避免后续重复读取
        return final_content

    def _check_exit_prompt(self, content: str) -> bool:
        """检查是否出现退出提示"""
        if not content:
            return False

        # 清理ANSI转义字符
        import re
        ansi_escape = re.compile(r'\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])')
        clean_content = ansi_escape.sub('', content).lower()

        # 检查退出相关的提示
        exit_indicators = [
            'exit without saving',
            'exit discarding changes',
            'discard changes and exit',
            'quit without saving',
            'exit setup without saving'
        ]

        for indicator in exit_indicators:
            if indicator in clean_content:
                print(f"发现退出提示: {indicator}")
                return True

        return False

    def _is_main_page(self, content: str) -> bool:
        """检查是否在主页面"""
        if not content:
            return False

        # 清理ANSI转义字符
        import re
        ansi_escape = re.compile(r'\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])')
        clean_content = ansi_escape.sub('', content).lower()

        # 主页面的特征
        main_page_indicators = [
            'main',  # Main TAB
            'system information',
            'processor information',
            'memory information',
            'bios setup utility',
            'setup utility'
        ]

        # TAB导航行的特征（包含多个TAB名称）
        tab_keywords = ['main', 'advanced', 'platform configuration', 'socket configuration',
                       'server mgmt', 'security', 'boot', 'save & exit']

        # 检查是否包含多个TAB关键词（说明在TAB导航界面）
        keyword_count = sum(1 for keyword in tab_keywords if keyword in clean_content)
        if keyword_count >= 3:  # 如果包含3个或更多TAB关键词
            print(f"检测到TAB导航界面，包含 {keyword_count} 个TAB关键词")
            return True

        # 检查主页面特征
        for indicator in main_page_indicators:
            if indicator in clean_content:
                print(f"检测到主页面特征: {indicator}")
                return True

        return False

    def handle_bios_prompts(self, content: str) -> bool:
        """处理各种BIOS提示和对话框"""
        if not content:
            return False

        # 清理ANSI转义字符
        import re
        ansi_escape = re.compile(r'\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])')
        clean_content = ansi_escape.sub('', content).lower()

        # 定义各种提示和对应的处理方式
        prompt_handlers = [
            {
                'indicators': ['exit without saving', 'discard changes and exit', 'quit without saving'],
                'action': 'esc',
                'description': '退出提示'
            },
            {
                'indicators': ['save changes and exit', 'save and exit', 'save configuration'],
                'action': 'esc',
                'description': '保存退出提示'
            },
            {
                'indicators': ['are you sure', 'confirm', 'warning'],
                'action': 'esc',
                'description': '确认对话框'
            },
            {
                'indicators': ['press any key', 'press enter', 'continue'],
                'action': 'enter',
                'description': '继续提示'
            },
            {
                'indicators': ['loading', 'please wait', 'initializing'],
                'action': 'wait',
                'description': '加载提示'
            }
        ]

        for handler in prompt_handlers:
            for indicator in handler['indicators']:
                if indicator in clean_content:
                    print(f"检测到{handler['description']}: {indicator}")

                    if handler['action'] == 'esc':
                        print("发送ESC键处理提示")
                        self.send_key(KeyCode.ESC, 0.5)
                        return True
                    elif handler['action'] == 'enter':
                        print("发送Enter键继续")
                        self.send_key(KeyCode.ENTER, 0.5)
                        return True
                    elif handler['action'] == 'wait':
                        print("等待加载完成...")
                        time.sleep(2.0)
                        return True

        return False
    
    def explore_all_tabs(self) -> Dict:
        """遍历所有TAB页"""
        print("开始遍历所有TAB页...")
        
        # if self.tab_count == 0:
        #     self.detect_tab_count()

        # 回到主页面并获取内容，避免重复读取
        main_page_content = self.go_to_main_page()

        all_tabs_structure = {}

        # for tab_index in range(self.tab_count):
        for tab_index in range(1):
            print(f"\n=== 遍历TAB {tab_index + 1}/{self.tab_count} ===")

            # 切换到指定TAB
            if tab_index > 0:
                for _ in range(tab_index):
                    self.send_key(KeyCode.RIGHT)
                # 切换TAB后需要重新读取内容
                tab_content = self.read_screen_content()
            else:
                # 第一个TAB使用主页面内容
                tab_content = main_page_content

            self.current_tab = tab_index

            # # 调试当前TAB内容
            # print(f"\n=== 调试TAB {tab_index + 1} 内容 ===")
            # self.debug_screen_content(tab_content, save_to_file=True)

            print("1111", repr(tab_content))

            tab_name = self._extract_tab_name(tab_content, tab_index)

            print(f"识别的TAB名称: {tab_name}")
            print(f"TAB内容预览: {tab_content[:200]}...")  # 显示前200个字符
            
            # 遍历当前TAB的菜单结构
            tab_structure = self.explore_current_tab(tab_name, tab_index)
            all_tabs_structure[tab_name] = tab_structure
            
            # 回到主页面准备下一个TAB
            self.go_to_main_page()
        
        return all_tabs_structure
    
    def _extract_tab_name(self, content: str, tab_index: int) -> str:
        """从内容中提取TAB名称，处理ANSI转义字符"""
        import re

        # 清理ANSI转义序列的函数
        def clean_ansi(text):
            # 移除ANSI转义序列
            ansi_escape = re.compile(r'\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])')
            return ansi_escape.sub('', text)

        # 查找当前激活TAB的函数
        def find_active_tab(text):
            # 常见的BIOS TAB高亮模式
            patterns = [
                r'\[([^\]]+)\]',  # 方括号包围的TAB
                r'>\s*([^<\n]+?)\s*<',  # 尖括号包围的TAB
                r'\*\s*([^\*\n]+?)\s*\*',  # 星号包围的TAB
                r'(\w+(?:\s+\w+)*)\s*\|\s*',  # 竖线分隔的TAB
            ]

            for pattern in patterns:
                matches = re.findall(pattern, text, re.IGNORECASE)
                if matches:
                    for match in matches:
                        clean_match = clean_ansi(match).strip()
                        if any(keyword in clean_match.lower() for keyword in
                               ['main', 'advanced', 'platform configuration', 'socket configuration',
                                'server mgmt', 'security', 'boot', 'save & exit']):
                            return clean_match
            return None

        # 首先尝试查找激活的TAB
        active_tab = find_active_tab(content)
        if active_tab:
            print(f"检测到激活TAB: {active_tab}")
            return active_tab

        # 清理内容并按行处理
        clean_content = clean_ansi(content)
        lines = clean_content.split('\n')

        # 查找TAB行（通常在前几行）
        tab_keywords = ['main', 'advanced', 'platform configuration', 'socket configuration',
                       'server mgmt', 'security', 'boot', 'save & exit']

        for line_num, line in enumerate(lines[:15]):  # 检查前15行
            line = line.strip()
            if not line or len(line) > 100:  # 跳过空行和过长的行
                continue

            line_lower = line.lower()

            # 检查是否包含多个TAB关键词（可能是TAB导航行）
            keyword_count = sum(1 for keyword in tab_keywords if keyword in line_lower)

            if keyword_count >= 2:  # 如果包含2个或更多TAB关键词，可能是TAB导航行
                print(f"在第{line_num+1}行发现可能的TAB导航行: {line}")

                # 尝试解析TAB名称
                found_tabs = []

                # 方法1: 按空格分割并匹配关键词
                words = line.split()
                i = 0
                while i < len(words):
                    word_lower = words[i].lower()

                    # 检查单词关键词
                    for keyword in ['main', 'advanced', 'security', 'boot']:
                        if keyword == word_lower:
                            found_tabs.append(words[i].title())
                            break

                    # 检查复合关键词
                    if word_lower == 'platform' and i + 1 < len(words) and words[i+1].lower() == 'configuration':
                        found_tabs.append('Platform Configuration')
                        i += 1  # 跳过下一个词
                    elif word_lower == 'socket' and i + 1 < len(words) and words[i+1].lower() == 'configuration':
                        found_tabs.append('Socket Configuration')
                        i += 1
                    elif word_lower == 'server' and i + 1 < len(words) and words[i+1].lower() == 'mgmt':
                        found_tabs.append('Server Mgmt')
                        i += 1
                    elif word_lower == 'save' and i + 1 < len(words) and words[i+1].lower() == '&' and i + 2 < len(words) and words[i+2].lower() == 'exit':
                        found_tabs.append('Save & Exit')
                        i += 2

                    i += 1

                if found_tabs:
                    print(f"解析出的TAB名称: {found_tabs}")
                    # 根据tab_index选择对应的TAB
                    if len(found_tabs) > tab_index:
                        return found_tabs[tab_index]
                    else:
                        return found_tabs[0]

            # 检查单个TAB关键词
            for keyword in tab_keywords:
                if keyword in line_lower:
                    # 如果只包含一个关键词，可能是当前激活的TAB
                    if keyword_count == 1:
                        print(f"在第{line_num+1}行发现单个TAB关键词: {keyword}")
                        return keyword.title()

        # 最后的备选方案：查找包含关键词的任何行
        for line in lines[:10]:
            line_clean = line.strip()
            if line_clean and len(line_clean) < 50:
                for keyword in tab_keywords:
                    if keyword in line_clean.lower():
                        print(f"备选方案找到TAB: {line_clean}")
                        return line_clean.title()

        print(f"未能识别TAB名称，使用默认名称: Tab_{tab_index + 1}")
        return f"Tab_{tab_index + 1}"
    
    def explore_current_tab(self, tab_name: str, tab_index: int) -> Dict:
        """遍历当前TAB的菜单结构"""
        print(f"遍历TAB: {tab_name}")
        
        root_content = self.read_screen_content()
        
        # 创建根节点
        root_node = MenuNode(
            name=tab_name,
            path=f"/{tab_name}",
            level=0,
            tab_index=tab_index,
            parent_path="",
            children=[],
            content=root_content
        )
        
        # 递归遍历子菜单
        self._explore_submenu(root_node)
        
        return asdict(root_node)
    
    def _explore_submenu(self, parent_node: MenuNode):
        """递归遍历子菜单"""
        print(f"遍历子菜单: {parent_node.path}")
        
        # 防止无限递归
        if parent_node.level > 5:  # 最大深度限制
            return
        
        if parent_node.path in self.visited_paths:
            return
        
        self.visited_paths.add(parent_node.path)
        
        # 尝试向下导航，寻找菜单项
        menu_items = self._find_menu_items()
        
        for item_index, item_name in enumerate(menu_items):
            # 导航到菜单项
            self._navigate_to_item(item_index)
            
            # 尝试进入子菜单
            if self._try_enter_submenu():
                # 成功进入子菜单
                submenu_content = self.read_screen_content()
                
                child_node = MenuNode(
                    name=item_name,
                    path=f"{parent_node.path}/{item_name}",
                    level=parent_node.level + 1,
                    tab_index=parent_node.tab_index,
                    parent_path=parent_node.path,
                    children=[],
                    content=submenu_content,
                    is_submenu=True
                )
                
                parent_node.children.append(child_node)
                
                # 递归遍历子菜单
                self._explore_submenu(child_node)
                
                # 返回上级菜单
                self.send_key(KeyCode.ESC)
        
    def _find_menu_items(self) -> List[str]:
        """查找当前页面的菜单项"""
        content = self.read_screen_content()
        
        # 这里需要根据实际BIOS界面格式来解析菜单项
        # 简单实现，寻找可能的菜单项
        lines = content.split('\n')
        menu_items = []
        
        for line in lines:
            line = line.strip()
            if line and len(line) < 100:  # 过滤掉太长的行
                # 简单的启发式规则识别菜单项
                if any(char in line for char in ['>', '[', ']', ':']):
                    menu_items.append(line)
        
        return menu_items[:20]  # 限制菜单项数量
    
    def _navigate_to_item(self, item_index: int):
        """导航到指定的菜单项"""
        # 先回到菜单顶部
        for _ in range(20):
            self.send_key(KeyCode.UP, 0.1)
        
        # 向下导航到指定项
        for _ in range(item_index):
            self.send_key(KeyCode.DOWN, 0.1)
    
    def _try_enter_submenu(self) -> bool:
        """尝试进入子菜单，智能处理各种提示"""
        before_content = self.read_screen_content()

        # 发送回车键
        self.send_key(KeyCode.ENTER)
        time.sleep(0.5)  # 等待响应

        after_content = self.read_screen_content()

        # 检查是否出现提示需要处理
        max_prompt_attempts = 3
        for attempt in range(max_prompt_attempts):
            if self.handle_bios_prompts(after_content):
                print(f"处理提示后重新读取内容 (尝试 {attempt + 1})")
                time.sleep(0.5)
                after_content = self.read_screen_content()
            else:
                break

        # 判断是否成功进入子菜单
        content_changed = not self._is_similar_content(before_content, after_content)

        if content_changed:
            print("✅ 成功进入子菜单")
        else:
            print("❌ 未能进入子菜单或内容未变化")

        return content_changed
    
    def save_menu_structure(self, structure: Dict, filename: str = "bios_menu_structure.json"):
        """保存菜单结构到文件"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(structure, f, ensure_ascii=False, indent=2)
            print(f"菜单结构已保存到: {filename}")
        except Exception as e:
            print(f"保存菜单结构失败: {e}")
    
    def close_sol_session(self):
        """关闭SOL会话"""
        if self.sol_process:
            try:
                self.sol_process.terminate()
                self.sol_process.wait(timeout=5)
            except:
                self.sol_process.kill()
            finally:
                self.sol_process = None
        print("SOL会话已关闭")
    
    def run_full_exploration(self) -> Dict:
        """运行完整的BIOS菜单遍历"""
        print("开始BIOS菜单结构遍历...")
        
        try:
            if not self.start_sol_session():
                return {}
            
            # 等待BIOS界面稳定
            time.sleep(2)
            
            # 遍历所有TAB页
            menu_structure = self.explore_all_tabs()
            
            # 保存结果
            self.save_menu_structure(menu_structure)
            
            return menu_structure
            
        except Exception as e:
            print(f"遍历过程中出错: {e}")
            return {}
        finally:
            self.close_sol_session()


def main():
    """主函数"""
    # 配置参数（从simple_git_bios_info.py中获取）
    HOST = "************"
    USERNAME = "Administrator"
    PASSWORD = "Superuser9!"
    
    # 创建遍历器实例
    explorer = BIOSMenuExplorer(HOST, USERNAME, PASSWORD)
    
    # 运行完整遍历
    menu_structure = explorer.run_full_exploration()
    
    if menu_structure:
        print("\n=== BIOS菜单结构遍历完成 ===")
        print(f"发现 {len(menu_structure)} 个主要TAB页")
        
        for tab_name, tab_data in menu_structure.items():
            print(f"\nTAB: {tab_name}")
            print(f"  子菜单数量: {len(tab_data.get('children', []))}")
    else:
        print("菜单结构遍历失败")


if __name__ == "__main__":
    main()
