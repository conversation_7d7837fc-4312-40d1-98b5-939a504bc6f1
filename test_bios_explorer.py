#!/usr/bin/env python3
"""
BIOS菜单遍历器测试文件
用于测试各个功能模块的正确性
"""

import unittest
import json
import tempfile
import os
from unittest.mock import Mock, patch
from bios_menu_explorer import BIOSMenuExplorer, KeyCode, MenuNode
from config import validate_config, get_sol_command, SOL_CONFIG


class TestBIOSMenuExplorer(unittest.TestCase):
    """BIOS菜单遍历器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.explorer = BIOSMenuExplorer(
            host="test_host",
            username="test_user", 
            password="test_pass"
        )
    
    def test_key_codes(self):
        """测试按键代码"""
        self.assertEqual(KeyCode.UP.value, '\x1b[A')
        self.assertEqual(KeyCode.DOWN.value, '\x1b[B')
        self.assertEqual(KeyCode.RIGHT.value, '\x1b[C')
        self.assertEqual(KeyCode.LEFT.value, '\x1b[D')
        self.assertEqual(KeyCode.ENTER.value, '\r')
        self.assertEqual(KeyCode.ESC.value, '\x1b')
    
    def test_menu_node_creation(self):
        """测试菜单节点创建"""
        node = MenuNode(
            name="Test Menu",
            path="/Test",
            level=1,
            tab_index=0,
            parent_path="/",
            children=[],
            content="Test content"
        )
        
        self.assertEqual(node.name, "Test Menu")
        self.assertEqual(node.path, "/Test")
        self.assertEqual(node.level, 1)
        self.assertEqual(node.tab_index, 0)
        self.assertFalse(node.is_submenu)
        self.assertFalse(node.is_configurable)
    
    @patch('subprocess.Popen')
    def test_start_sol_session_success(self, mock_popen):
        """测试SOL会话启动成功"""
        # 模拟成功的进程
        mock_process = Mock()
        mock_process.poll.return_value = None  # 进程正在运行
        mock_process.stdout.fileno.return_value = 1
        mock_process.stderr.fileno.return_value = 2
        mock_popen.return_value = mock_process
        
        with patch('fcntl.fcntl'), patch('time.sleep'):
            result = self.explorer.start_sol_session()
        
        self.assertTrue(result)
        self.assertIsNotNone(self.explorer.sol_process)
    
    @patch('subprocess.Popen')
    def test_start_sol_session_failure(self, mock_popen):
        """测试SOL会话启动失败"""
        # 模拟失败的进程
        mock_process = Mock()
        mock_process.poll.return_value = 1  # 进程已退出
        mock_process.communicate.return_value = ("", "Connection failed")
        mock_process.returncode = 1
        mock_popen.return_value = mock_process
        
        with patch('time.sleep'):
            result = self.explorer.start_sol_session()
        
        self.assertFalse(result)
    
    def test_send_key_no_process(self):
        """测试在没有进程时发送按键"""
        result = self.explorer.send_key(KeyCode.UP)
        self.assertFalse(result)
    
    @patch('subprocess.Popen')
    def test_send_key_with_process(self, mock_popen):
        """测试有进程时发送按键"""
        mock_process = Mock()
        mock_process.poll.return_value = None
        mock_process.stdin.write = Mock()
        mock_process.stdin.flush = Mock()
        mock_popen.return_value = mock_process
        
        self.explorer.sol_process = mock_process
        
        with patch('time.sleep'):
            result = self.explorer.send_key(KeyCode.UP)
        
        self.assertTrue(result)
        mock_process.stdin.write.assert_called_once_with(KeyCode.UP.value)
        mock_process.stdin.flush.assert_called_once()
    
    def test_extract_tab_name(self):
        """测试TAB名称提取"""
        content = """
        BIOS Setup Utility
        Main    Advanced    Security    Boot    Exit
        System Information
        """
        
        tab_name = self.explorer._extract_tab_name(content, 0)
        self.assertIn("Main", tab_name)
    
    def test_is_similar_content(self):
        """测试内容相似度判断"""
        content1 = "Main Menu System Information BIOS Version"
        content2 = "Main Menu System Information BIOS Version 2.0"
        content3 = "Advanced Menu Chipset Configuration"
        
        # 相似内容
        self.assertTrue(self.explorer._is_similar_content(content1, content2))
        
        # 不相似内容
        self.assertFalse(self.explorer._is_similar_content(content1, content3))
        
        # 空内容
        self.assertFalse(self.explorer._is_similar_content("", content1))
        self.assertFalse(self.explorer._is_similar_content(content1, ""))
    
    def test_find_menu_items(self):
        """测试菜单项查找"""
        # 模拟屏幕内容
        mock_content = """
        > System Information
        > Processor Information  
        > Memory Information
        [ ] Boot Option #1
        CPU Temperature: 45C
        Fan Speed: 1200 RPM
        """
        
        with patch.object(self.explorer, 'read_screen_content', return_value=mock_content):
            menu_items = self.explorer._find_menu_items()
        
        self.assertGreater(len(menu_items), 0)
        # 检查是否包含预期的菜单项
        menu_text = ' '.join(menu_items)
        self.assertIn('System Information', menu_text)
    
    def test_save_menu_structure(self):
        """测试菜单结构保存"""
        test_structure = {
            "Main": {
                "name": "Main",
                "path": "/Main",
                "children": []
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.json') as f:
            temp_filename = f.name
        
        try:
            self.explorer.save_menu_structure(test_structure, temp_filename)
            
            # 验证文件是否正确保存
            with open(temp_filename, 'r', encoding='utf-8') as f:
                loaded_structure = json.load(f)
            
            self.assertEqual(loaded_structure, test_structure)
        finally:
            os.unlink(temp_filename)
    
    def test_close_sol_session(self):
        """测试SOL会话关闭"""
        mock_process = Mock()
        mock_process.terminate = Mock()
        mock_process.wait = Mock()
        
        self.explorer.sol_process = mock_process
        self.explorer.close_sol_session()
        
        mock_process.terminate.assert_called_once()
        mock_process.wait.assert_called_once()
        self.assertIsNone(self.explorer.sol_process)


class TestConfig(unittest.TestCase):
    """配置测试类"""
    
    def test_validate_config_success(self):
        """测试配置验证成功"""
        errors = validate_config()
        self.assertEqual(len(errors), 0)
    
    def test_get_sol_command(self):
        """测试SOL命令生成"""
        cmd = get_sol_command()
        
        expected_elements = [
            'ipmitool', '-I', 'lanplus',
            '-H', SOL_CONFIG['host'],
            '-U', SOL_CONFIG['username'],
            '-P', SOL_CONFIG['password'],
            'sol', 'activate'
        ]
        
        self.assertEqual(cmd, expected_elements)
    
    def test_get_sol_command_custom_config(self):
        """测试自定义配置的SOL命令生成"""
        custom_config = {
            'interface': 'lan',
            'host': 'custom_host',
            'username': 'custom_user',
            'password': 'custom_pass'
        }
        
        cmd = get_sol_command(custom_config)
        
        self.assertIn('custom_host', cmd)
        self.assertIn('custom_user', cmd)
        self.assertIn('custom_pass', cmd)
        self.assertIn('lan', cmd)


class TestMenuStructureAnalysis(unittest.TestCase):
    """菜单结构分析测试类"""
    
    def setUp(self):
        """准备测试数据"""
        self.sample_structure = {
            "Main": {
                "name": "Main",
                "path": "/Main",
                "level": 0,
                "tab_index": 0,
                "parent_path": "",
                "children": [
                    {
                        "name": "System Information",
                        "path": "/Main/System Information",
                        "level": 1,
                        "tab_index": 0,
                        "parent_path": "/Main",
                        "children": [],
                        "content": "System info content",
                        "is_submenu": True
                    },
                    {
                        "name": "Processor",
                        "path": "/Main/Processor", 
                        "level": 1,
                        "tab_index": 0,
                        "parent_path": "/Main",
                        "children": [
                            {
                                "name": "CPU Settings",
                                "path": "/Main/Processor/CPU Settings",
                                "level": 2,
                                "tab_index": 0,
                                "parent_path": "/Main/Processor",
                                "children": [],
                                "content": "CPU settings content",
                                "is_submenu": True
                            }
                        ],
                        "content": "Processor content",
                        "is_submenu": True
                    }
                ],
                "content": "Main menu content",
                "is_submenu": False
            }
        }
    
    def test_structure_depth_calculation(self):
        """测试结构深度计算"""
        from example_usage import count_menus_and_depth
        
        main_tab = self.sample_structure["Main"]
        count, depth = count_menus_and_depth(main_tab)
        
        # 应该有4个菜单项: Main, System Information, Processor, CPU Settings
        self.assertEqual(count, 4)
        # 最大深度应该是2 (CPU Settings在第2级)
        self.assertEqual(depth, 2)
    
    def test_menu_summary_creation(self):
        """测试菜单摘要创建"""
        # 创建临时文件保存测试结构
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.json') as f:
            json.dump(self.sample_structure, f)
            temp_filename = f.name
        
        try:
            # 模拟文件读取
            with patch('builtins.open', unittest.mock.mock_open(read_data=json.dumps(self.sample_structure))):
                from example_usage import count_menus_and_depth
                
                main_tab = self.sample_structure["Main"]
                count, depth = count_menus_and_depth(main_tab)
                
                self.assertGreater(count, 0)
                self.assertGreaterEqual(depth, 0)
        finally:
            os.unlink(temp_filename)


class TestIntegration(unittest.TestCase):
    """集成测试类"""
    
    @patch('subprocess.Popen')
    @patch('time.sleep')
    @patch('select.select')
    @patch('os.read')
    def test_full_exploration_mock(self, mock_read, mock_select, _, mock_popen):
        """测试完整遍历流程（模拟）"""
        # 设置模拟
        mock_process = Mock()
        mock_process.poll.return_value = None
        mock_process.stdout.fileno.return_value = 1
        mock_process.stderr.fileno.return_value = 2
        mock_process.stdin.write = Mock()
        mock_process.stdin.flush = Mock()
        mock_popen.return_value = mock_process
        
        # 模拟屏幕内容读取
        mock_select.return_value = ([mock_process.stdout], [], [])
        mock_read.return_value = b"Main Menu\n> System Information\n> Boot Options"
        
        explorer = BIOSMenuExplorer("test", "test", "test")
        
        with patch('fcntl.fcntl'):
            # 测试启动
            result = explorer.start_sol_session()
            self.assertTrue(result)
            
            # 测试TAB检测
            with patch.object(explorer, '_is_similar_content', return_value=True):
                tab_count = explorer.detect_tab_count()
                self.assertGreater(tab_count, 0)
            
            # 测试内容读取
            content = explorer.read_screen_content()
            self.assertIsInstance(content, str)
            
            # 清理
            explorer.close_sol_session()


def run_tests():
    """运行所有测试"""
    print("运行BIOS菜单遍历器测试...")
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestBIOSMenuExplorer,
        TestConfig,
        TestMenuStructureAnalysis,
        TestIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出结果摘要
    print(f"\n测试结果摘要:")
    print(f"  运行测试数: {result.testsRun}")
    print(f"  失败数: {len(result.failures)}")
    print(f"  错误数: {len(result.errors)}")
    print(f"  成功率: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    
    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback.split('AssertionError:')[-1].strip()}")
    
    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback.split('Exception:')[-1].strip()}")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_tests()
    exit(0 if success else 1)
